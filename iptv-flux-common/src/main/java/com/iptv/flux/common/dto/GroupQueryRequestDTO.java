package com.iptv.flux.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分组查询请求DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分组查询请求参数")
public class GroupQueryRequestDTO {

    @Schema(description = "分组名称", example = "VIP用户组")
    private String groupName;

    @Schema(description = "业务ID", example = "biz001")
    private String businessId;

    @Schema(description = "来源", example = "dx",
            allowableValues = {"dx", "yd", "lt", "other"})
    private String source;

    /**
     * 检查是否为空查询（所有参数都为空）
     */
    public boolean isEmpty() {
        return (groupName == null || groupName.trim().isEmpty()) &&
               (businessId == null || businessId.trim().isEmpty()) &&
               (source == null || source.trim().isEmpty());
    }

    /**
     * 获取非空的分组名称
     */
    public String getTrimmedGroupName() {
        return groupName != null ? groupName.trim() : null;
    }

    /**
     * 获取非空的业务ID
     */
    public String getTrimmedBusinessId() {
        return businessId != null ? businessId.trim() : null;
    }

    /**
     * 获取非空的来源
     */
    public String getTrimmedSource() {
        return source != null ? source.trim() : null;
    }
}
