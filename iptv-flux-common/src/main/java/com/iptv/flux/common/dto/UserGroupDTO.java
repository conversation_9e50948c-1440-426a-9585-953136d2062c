package com.iptv.flux.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.dto
 * @className: UserGroupDTO
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:49
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserGroupDTO {
    private String userId;
    private String source;
    private Set<String> groupIds;
}
