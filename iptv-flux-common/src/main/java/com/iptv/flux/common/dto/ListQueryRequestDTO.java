package com.iptv.flux.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 列表查询请求DTO
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "列表查询请求参数")
public class ListQueryRequestDTO {

    @Schema(description = "用户ID过滤", example = "user123")
    private String userId;

    @Schema(description = "来源过滤", example = "telecom", 
            allowableValues = {"telecom", "unicom", "mobile", "other"})
    private String source;

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    @Schema(description = "当前页码", example = "1", required = true)
    private Integer page;

    @NotNull(message = "每页条数不能为空")
    @Min(value = 1, message = "每页条数必须大于0")
    @Max(value = 100, message = "每页条数不能超过100")
    @Schema(description = "每页条数", example = "10", required = true)
    private Integer pageSize;
}
