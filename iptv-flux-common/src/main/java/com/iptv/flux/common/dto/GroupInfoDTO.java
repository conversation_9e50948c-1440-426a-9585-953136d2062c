package com.iptv.flux.common.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分组信息DTO
 *
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分组信息")
public class GroupInfoDTO {

    @Schema(description = "分组ID", example = "GROUP_001")
    private String groupId;

    @Schema(description = "策略ID", example = "STRATEGY_001")
    private String strategyId;

    @Schema(description = "分组名称", example = "高价值用户群")
    private String groupName;

    @Schema(description = "分组描述", example = "高价值用户群体描述")
    private String description;

    // ==================== 业务方法 ====================

    /**
     * 检查是否有有效的策略ID
     */
    @JsonIgnore
    public boolean hasStrategyId() {
        return strategyId != null && !strategyId.trim().isEmpty();
    }

    /**
     * 获取非空的策略ID
     */
    @JsonIgnore
    public String getTrimmedStrategyId() {
        return strategyId != null ? strategyId.trim() : null;
    }

    /**
     * 创建分组信息
     */
    public static GroupInfoDTO create(String groupId, String strategyId, String groupName, String description) {
        return GroupInfoDTO.builder()
                .groupId(groupId)
                .strategyId(strategyId)
                .groupName(groupName)
                .description(description)
                .build();
    }

    /**
     * 复制并更新策略ID
     */
    public GroupInfoDTO copyWithStrategyId(String newStrategyId) {
        return GroupInfoDTO.builder()
                .groupId(this.groupId)
                .strategyId(newStrategyId)
                .groupName(this.groupName)
                .description(this.description)
                .build();
    }
}
