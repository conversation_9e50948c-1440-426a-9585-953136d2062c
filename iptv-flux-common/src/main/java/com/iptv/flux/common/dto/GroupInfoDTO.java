package com.iptv.flux.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.dto
 * @className: GroupInfoDTO
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:48
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupInfoDTO {
    private String groupId;
    private String businessId;
    private String groupName;
    private String description;
}
