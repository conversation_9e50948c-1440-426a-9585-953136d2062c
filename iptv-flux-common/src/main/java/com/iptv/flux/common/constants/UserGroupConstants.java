package com.iptv.flux.common.constants;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.common.constants
 * @className: UserGroupConstants
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:48
 * @version: 1.0
 */
public final class UserGroupConstants {
    private UserGroupConstants() {}

    // Redis key patterns
    public static final String USER_GROUP_HASH_KEY_PREFIX = "ug:hash";
    public static final String GROUP_INFO_HASH_KEY_PREFIX = "g:hash";
    public static final String BLOOM_FILTER_KEY = "ug:bloom";
    public static final String LOCK_SUFFIX = ":lock";

    // Cache related constants
    public static final String EMPTY_RESULT = "EMPTY";
    public static final long REDIS_LOCK_WAIT_MS = 200;
    public static final long REDIS_LOCK_LEASE_MS = 5000;

    // Metrics names
    public static final String DB_QUERY_TIMER = "user.group.db.query.time";
    public static final String CACHE_HIT_METRIC = "user.group.cache.hit";
    public static final String REDIS_HIT_METRIC = "user.group.redis.hit";
    public static final String CACHE_MISS_METRIC = "user.group.cache.miss";
    public static final String QUERY_TOTAL_METRIC = "user.group.query.total";
    public static final String QUERY_ERROR_METRIC = "user.group.query.error";
    public static final String BLOOM_FILTER_HIT_METRIC = "user.group.bloom.hit";

    // Resilience constants
    public static final String CIRCUIT_BREAKER_NAME = "userGroupService";
    public static final String RATE_LIMITER_NAME = "userGroupRateLimiter";

    // Executor related constants
    public static final String ASYNC_EXECUTOR_BEAN = "cacheRefreshExecutor";

    // HTTP related constants
    public static final int MAX_HTTP_CONNECTIONS = 5000;
    public static final int CONNECTION_TIMEOUT = 3000;
    public static final int READ_TIMEOUT = 3000;

    // API paths
    public static final String API_BASE_PATH = "/api/usergroup";
    public static final String API_CACHE_PATH = "/api/cache";
    public static final String API_MONITOR_PATH = "/api/monitor";
    public static final String API_DASHBOARD_PATH = "/api/dashboard";
    public static final String API_DATA_IMPORT_PATH = "/api/data-import";

    // Result codes
    public static final int SUCCESS_CODE = 200;
    public static final int ERROR_CODE = 500;
    public static final int NOT_FOUND_CODE = 404;
    public static final int RATE_LIMIT_EXCEEDED_CODE = 429;

    // 布隆过滤器相关
    public static final String BLOOM_FILTER_AUTO_COMPLETE_METRIC = "user.group.bloom.auto.complete";
    public static final String BLOOM_FILTER_POTENTIAL_BYPASS_METRIC = "user.group.bloom.potential.bypass";
    public static final String BLOOM_FILTER_SYNC_SUCCESS_METRIC = "user.group.bloom.sync.success";
    public static final String BLOOM_FILTER_SYNC_ERROR_METRIC = "user.group.bloom.sync.error";

    // 缓存穿透防护相关
    public static final String CACHE_PENETRATION_RATE_LIMIT_METRIC = "user.group.cache.penetration.rate.limit";
    public static final String KNOWN_MISSING_KEYS_HIT_METRIC = "user.group.known.missing.keys.hit";

    // 缓存预热相关
    public static final String CACHE_WARMUP_GLOBAL_LOCK = "cache:warmup:global_lock";
    public static final String CACHE_WARMUP_PROGRESS_KEY = "cache:warmup:global_progress";
    public static final String CACHE_WARMUP_COVERAGE_KEY = "cache:warmup:global_coverage_rate";

    // 数据导入相关
    public static final String DATA_IMPORT_LOCK_PREFIX = "data:import:lock:";
    public static final String DATA_IMPORT_PROGRESS_PREFIX = "data:import:progress:";
    public static final String DATA_IMPORT_RESULT_PREFIX = "data:import:result:";
    public static final int DATA_IMPORT_BATCH_SIZE = 1000;
    public static final int DATA_IMPORT_MAX_FILE_SIZE_MB = 200;
    public static final String[] ALLOWED_FILE_EXTENSIONS = {".xlsx", ".xls"};
    public static final String DEFAULT_SOURCE = "dx";

    // 分组自动创建相关监控指标
    public static final String GROUP_AUTO_CREATE_SUCCESS_METRIC = "group.auto.create.success";
    public static final String GROUP_AUTO_CREATE_ERROR_METRIC = "group.auto.create.error";
    public static final String GROUP_AUTO_UPDATE_SUCCESS_METRIC = "group.auto.update.success";
    public static final String GROUP_AUTO_UPDATE_SKIP_METRIC = "group.auto.update.skip";
    public static final String GROUP_BATCH_CREATE_TOTAL_METRIC = "group.batch.create.total";
    public static final String GROUP_BATCH_CREATE_SUCCESS_METRIC = "group.batch.create.success";
    public static final String GROUP_BATCH_CREATE_ERROR_METRIC = "group.batch.create.error";

    // 分组信息服务相关监控指标
    public static final String GROUP_INFO_CACHE_HIT_METRIC = "service.groupinfo.cache.hit";
    public static final String GROUP_INFO_CACHE_MISS_METRIC = "service.groupinfo.cache.miss";
    public static final String GROUP_INFO_CACHE_ERROR_METRIC = "service.groupinfo.cache.error";
    public static final String GROUP_INFO_CACHE_UPDATE_ERROR_METRIC = "service.groupinfo.cache.update.error";

    // 数据导入相关监控指标
    public static final String DATA_IMPORT_ERROR_METRIC = "dataimport.error";
    public static final String DATA_IMPORT_SUCCESS_METRIC = "dataimport.success";
    public static final String DATA_IMPORT_TOTAL_METRIC = "dataimport.total";
    public static final String DATA_IMPORT_LOG_SAVE_SUCCESS_METRIC = "dataimport.log.save.success";
    public static final String DATA_IMPORT_LOG_SAVE_ERROR_METRIC = "dataimport.log.save.error";
    public static final String DATA_IMPORT_LOG_UPDATE_ERROR_METRIC = "dataimport.log.update.error";

    // 仪表盘服务相关监控指标
    public static final String DASHBOARD_STATISTICS_ERROR_METRIC = "dashboard.statistics.service.error";
    public static final String DASHBOARD_TOP_GROUPS_ERROR_METRIC = "dashboard.topGroups.service.error";
    public static final String DASHBOARD_ACTIVITIES_ERROR_METRIC = "dashboard.activities.service.error";
    public static final String DASHBOARD_IMPORT_STATS_ERROR_METRIC = "dashboard.importStats.service.error";

    // 黑白名单相关监控指标
    public static final String BLACKWHITELIST_CACHE_HIT_METRIC = "blackwhitelist.cache.hit";
    public static final String BLACKWHITELIST_BLACKLIST_HIT_METRIC = "blackwhitelist.blacklist.hit";
    public static final String BLACKWHITELIST_WHITELIST_HIT_METRIC = "blackwhitelist.whitelist.hit";
    public static final String BLACKWHITELIST_CHECK_ERROR_METRIC = "blackwhitelist.check.error";

    // 其他服务监控指标
    public static final String INVALID_USER_ID_METRIC = "invalid.user.id";
    public static final String ACTIVE_CONNECTIONS_METRIC = "active.connections";

    // API性能监控指标
    public static final String API_USERGROUP_GET_TIMER = "api.usergroup.get";
    public static final String API_USERGROUP_DETAIL_GET_TIMER = "api.usergroup.detail.get";
    public static final String API_USERGROUP_SAVE_TIMER = "api.usergroup.save";
    public static final String API_GROUP_SAVE_TIMER = "api.group.save";

    // 缓存预热性能监控指标
    public static final String CACHE_WARMUP_DURATION_TIMER = "cache.warmup.duration";
    public static final String CACHE_WARMUP_EXECUTION_TIME_TIMER = "cache.warmup.execution.time";
    public static final String CACHE_WARMUP_PROCESSED_COUNT_METRIC = "cache.warmup.processed.count";
    public static final String CACHE_WARMUP_SUCCESS_COUNT_METRIC = "cache.warmup.success.count";
    public static final String CACHE_WARMUP_ERROR_COUNT_METRIC = "cache.warmup.error.count";

    // 数据库性能监控指标
    public static final String REPOSITORY_USERGROUP_QUERY_GROUPS_TIMER = "repository.usergroup.queryGroups";
    public static final String REPOSITORY_GROUP_FIND_BY_IDS_TIMER = "repository.group.findByIds";
    public static final String REPOSITORY_USERGROUP_SAVE_TIMER = "repository.usergroup.save";
    public static final String REPOSITORY_ERROR_METRIC = "repository.error";
    public static final String REPOSITORY_DASHBOARD_STATISTICS_ERROR_METRIC = "repository.dashboard.statistics.error";
    public static final String REPOSITORY_DASHBOARD_OPERATORS_ERROR_METRIC = "repository.dashboard.operators.error";

    // 服务层性能监控指标
    public static final String SERVICE_USERGROUP_LOAD_TIMER = "service.usergroup.load";
    public static final String SERVICE_USERGROUP_SAVE_TIMER = "service.usergroup.save";
    public static final String SERVICE_GROUPINFO_GETINFO_TIMER = "service.groupinfo.getinfo";
    public static final String SERVICE_GROUPINFO_BATCH_TIMER = "service.groupinfo.batch";

    // 锁释放监控指标
    public static final String LOCK_RELEASE_WRONG_THREAD_METRIC = "lock.release.wrong_thread";
    public static final String LOCK_RELEASE_ALREADY_RELEASED_METRIC = "lock.release.already_released";
    public static final String LOCK_RELEASE_ERROR_METRIC = "lock.release.error";
}
