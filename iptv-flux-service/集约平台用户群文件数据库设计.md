好的，我把文档里的“三类核心数据”（用户群、策略、用户-群关系）落成一套 **MySQL 8.0** 的表结构，并补充了通知/文件校验与留存的配套设计、索引与清理策略。表设计遵循文档中的字段与文件/消息格式（文件命名、QC=MD5、Action=create/update/delete、全量导出 groups/strategy/userGroup、更新通知等）。

---

# 一、总体建模思路（对应文档）

* **用户群数据**：群元数据（名称、运营商等）+ 多次生成的“群文件快照”（覆盖数、文件URL、MD5、生成时间）。QC 用于文件完整性校验。文件命名与生成时间遵循规范。
* **策略数据**：策略主表 + “策略-群”绑定明细（每个策略可以绑定多个群，每个绑定携带对应文件URL、覆盖数、MD5、生成时间等）。Action 支持 create/update/delete。
* **用户-群关系数据**：来自 userGroup.txt 的全量映射（UserID → GroupList），支持增量/全量来源标注与时间戳。UserID 可能为加密/约束ID，预留 hash\_method/key\_id 字段。
* **辅助与治理**：

  * 更新通知消息（一次通知给出三类全量文件的FTP路径，及分运营商文件路径）。
  * 文件留存（“暂定保留30天”），以“群文件快照”为主执行淘汰，但要避免删除仍被“生效策略”引用的快照。

---

# 二、核心表结构（DDL）

> 字符集统一 `utf8mb4`, 存储时间采用 `DATETIME`，同时保留原始 14 位时间串（便于追溯，如 `20250101080101`）。文档中的运营商 dx/lt/yd 与 “电信/联通/移动”兼容，统一用 `ENUM('dx','lt','yd')` 及名称映射。文件MD5固定32位。

```sql
-- 运营商字典
CREATE TABLE operator (
  operator_code ENUM('dx','lt','yd') NOT NULL PRIMARY KEY,
  name VARCHAR(16) NOT NULL COMMENT '电信/联通/移动'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

INSERT INTO operator(operator_code, name) VALUES
('dx','电信'),('lt','联通'),('yd','移动');
```

```sql
-- 1) 用户群主表（群元数据）
CREATE TABLE user_group (
  group_id VARCHAR(32) NOT NULL PRIMARY KEY,
  operator_code ENUM('dx','lt','yd') NOT NULL,
  group_name VARCHAR(128) NOT NULL,
  platform VARCHAR(128) NULL COMMENT '可视化平台/精准营销平台等（可多值以逗号分隔或改JSON）',
  description TEXT NULL,
  active TINYINT(1) NOT NULL DEFAULT 1,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY uk_group_operator (group_id, operator_code),
  KEY idx_operator (operator_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来自groups.txt & 策略消息的群元数据';
```

（对应文档的 GroupID/GroupName/Platform/Desciption/SysID 等群信息。文件命名与运营商前缀规则参见文档。】

```sql
-- 1’) 用户群文件快照（群每次生成的.txt/.qc 对）
CREATE TABLE user_group_snapshot (
  snapshot_id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  group_id VARCHAR(32) NOT NULL,
  operator_code ENUM('dx','lt','yd') NOT NULL,
  file_url VARCHAR(512) NOT NULL,
  qc_file_url VARCHAR(512) NULL,
  coverage BIGINT NOT NULL,
  generate_time DATETIME NULL,
  generate_time_raw VARCHAR(14) NULL,
  md5sum CHAR(32) NOT NULL,
  verified TINYINT(1) NOT NULL DEFAULT 0 COMMENT 'QC校验通过标记',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_group_gen_md5 (group_id, generate_time, md5sum),
  KEY idx_group_time (group_id, generate_time),
  KEY idx_md5 (md5sum),
  CONSTRAINT fk_snapshot_group FOREIGN KEY (group_id)
    REFERENCES user_group(group_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来自群文件与QC(MD5)；支撑30天留存与校验';
```

（文档规定 .txt 保存用户账号、.qc 保存对应文件MD5；命名含时间戳与覆盖数。】

```sql
-- 2) 策略主表
CREATE TABLE strategy (
  strategy_id CHAR(36) NOT NULL PRIMARY KEY COMMENT '全局唯一UUID',
  strategy_name VARCHAR(128) NOT NULL,
  start_time DATETIME NOT NULL,
  start_time_raw VARCHAR(14) NULL,
  end_time DATETIME NOT NULL,
  end_time_raw VARCHAR(14) NULL,
  action ENUM('create','update','delete') NOT NULL,
  create_user VARCHAR(64) NOT NULL,
  create_time DATETIME NOT NULL,
  create_time_raw VARCHAR(14) NULL,
  operator_code ENUM('dx','lt','yd') NOT NULL,
  description TEXT NULL,
  status ENUM('active','ended','deleted') NOT NULL DEFAULT 'active',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  KEY idx_operator (operator_code),
  KEY idx_time (start_time, end_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来自策略消息与strategy.txt；Action=create/update/delete';
```

（字段与文档中策略消息一致：StrategyID/Name/StartDateTime/EndDateTime/Action/CreateUser/CreateDateTime/SysID 等。】

```sql
-- 2’) 策略-群绑定明细（承载 GroupArray）
CREATE TABLE strategy_group (
  id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  strategy_id CHAR(36) NOT NULL,
  group_id VARCHAR(32) NOT NULL,
  platform VARCHAR(128) NULL,
  file_url VARCHAR(512) NOT NULL,
  coverage BIGINT NOT NULL,
  generate_time DATETIME NULL,
  generate_time_raw VARCHAR(14) NULL,
  md5sum CHAR(32) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  UNIQUE KEY uk_strategy_group_md5 (strategy_id, group_id, md5sum),
  KEY idx_group (group_id),
  CONSTRAINT fk_sg_strategy FOREIGN KEY (strategy_id)
    REFERENCES strategy(strategy_id) ON DELETE CASCADE,
  CONSTRAINT fk_sg_group FOREIGN KEY (group_id)
    REFERENCES group_info(group_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='策略绑定的群清单（GroupArray），含URL/覆盖数/MD5/生成时间';
```

（对照文档的 GroupArray：BindGroupID/BindGroupName/Platform/Desciption/FileURL/UserCovering/GenerateTime/sumkey。群名与口径可在 user\_group 中维护。】

```sql
-- 3) 用户（可为加密/约束ID，保留方法与密钥标识）
CREATE TABLE user (
  user_id VARCHAR(128) NOT NULL PRIMARY KEY,
  hash_method VARCHAR(32) NULL,
  key_id VARCHAR(64) NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='备注：UserID可约束加密密钥';
```

```sql
-- 3’) 用户-群关系（来自 userGroup.txt 的全量关系）
CREATE TABLE user_group_membership (
  user_id VARCHAR(128) NOT NULL,
  group_id VARCHAR(32) NOT NULL,
  source ENUM('full','incremental','notification') NOT NULL DEFAULT 'full',
  effective_from DATETIME NULL,
  last_seen_at DATETIME NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (user_id, group_id),
  KEY idx_group (group_id),
  CONSTRAINT fk_ugm_user FOREIGN KEY (user_id)
    REFERENCES user(user_id) ON DELETE CASCADE,
  CONSTRAINT fk_ugm_group FOREIGN KEY (group_id)
    REFERENCES user_group(group_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='来自 userGroup.txt: 用户ID → GroupList';
```

（对应文档的 userGroup.txt：UserID、GroupList。】

---

# 三、配套与治理（可选但强烈建议）

```sql
-- A) 更新通知（一次通知包含三类全量文件URL）
CREATE TABLE update_notification (
  id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  update_time DATETIME NOT NULL,
  update_time_raw VARCHAR(14) NULL,
  groups_file_url VARCHAR(512) NOT NULL,
  strategies_file_url VARCHAR(512) NOT NULL,
  usergroup_file_url VARCHAR(512) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='流量平台更新通知（全量文件URL）';
```

```sql
-- A’) 通知中的分运营商文件
CREATE TABLE update_notification_operator_file (
  id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  notification_id BIGINT UNSIGNED NOT NULL,
  operator_code ENUM('dx','lt','yd') NOT NULL,
  file_url VARCHAR(512) NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  CONSTRAINT fk_unof_notification FOREIGN KEY (notification_id)
    REFERENCES update_notification(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知中“三大运营商分群信息”文件URL';
```

（字段对应 UpdateTime、GroupsFileURL、StrategysFilURL、userGroupFileURL 及“分运营商文件URL数组”。】

```sql
-- B) 文件入库与校验流水（便于审计/追踪）
CREATE TABLE file_ingest (
  file_id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT PRIMARY KEY,
  category ENUM('groups','strategy','userGroup','group_txt','group_qc') NOT NULL,
  operator_code ENUM('dx','lt','yd') NULL,
  path VARCHAR(512) NOT NULL,
  md5sum CHAR(32) NULL,
  size_bytes BIGINT NULL,
  generate_time DATETIME NULL,
  loaded_at DATETIME NULL,
  status ENUM('pending','loaded','failed','verified') NOT NULL DEFAULT 'pending',
  error_message TEXT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  KEY idx_category_time (category, generate_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='FTP→DB入库与QC校验过程记录';
```

```sql
-- C) 便捷视图：每个群的“最新快照”
CREATE VIEW v_group_latest_snapshot AS
SELECT s.*
FROM user_group_snapshot s
JOIN (
  SELECT group_id, MAX(generate_time) AS max_gen
  FROM user_group_snapshot
  GROUP BY group_id
) t ON t.group_id = s.group_id AND t.max_gen = s.generate_time;
```

---

# 四、字段映射速查（与文档对齐）

* **群（groups.txt / 群消息）→ user\_group / user\_group\_snapshot**

  * SysID → `operator_code`；GroupID → `group_id`；GroupName → `group_name`；Platform → `platform`；Desciption → `description`；FileURL → `file_url`；UserCovering → `coverage`；GenerateTime → `generate_time/_raw`；sumkey(MD5) → `md5sum`；Strategys → 通过 `strategy_group` 反查策略集合。

* **策略（策略消息 / strategy.txt）→ strategy / strategy\_group**

  * StrategyID/Name、StartDateTime/EndDateTime、Action、CreateUser/CreateDateTime、SysID → `strategy` 表；
  * GroupArray 中的 BindGroupID/Name/Platform/Desciption/FileURL/UserCovering/GenerateTime/sumkey → `strategy_group` + `user_group`（群名与口径统一维护在 `user_group`）。

* **用户-群关系（userGroup.txt）→ user / user\_group\_membership**

  * UserID（可约束加密密钥）→ `user.user_id` + `hash_method/key_id`；
  * GroupList（CG001,CG002, …）→ 多行插入至 `user_group_membership`。

* **更新通知消息** → `update_notification` / `update_notification_operator_file`（含三类全量文件URL与分运营商文件URL数组）。

---

# 五、索引与约束建议

* 高频查询键：

  * 群维度：`user_group(group_id)`，`user_group_snapshot(group_id, generate_time)`
  * 策略维度：`strategy(strategy_id)`，`strategy_group(strategy_id, group_id)`
  * 用户关系：`user_group_membership(user_id)`、`idx_group`
* 唯一性：

  * `user_group_snapshot` 保证 `(group_id, generate_time, md5sum)` 唯一，避免重复入库同一次快照。
  * `strategy_group` 保证 `(strategy_id, group_id, md5sum)` 唯一，确保一次策略-群绑定的文件版本唯一。
* 参照完整性：全部 FK 已配置，清理时能级联。

---

# 六、留存与清理（30天）

> 文档“群文件保留周期暂定30天”。建议仅对 `user_group_snapshot` 做周期清理，但**保留仍被“active”策略引用的快照**。

```sql
-- 启用 MySQL 事件调度器（一次性）
-- SET GLOBAL event_scheduler = ON;

CREATE EVENT ev_prune_group_snapshots
ON SCHEDULE EVERY 1 DAY
DO
  DELETE s FROM user_group_snapshot s
  LEFT JOIN strategy_group sg
    ON sg.group_id = s.group_id AND sg.md5sum = s.md5sum
  LEFT JOIN strategy st
    ON st.strategy_id = sg.strategy_id AND st.status = 'active'
  WHERE s.created_at < NOW() - INTERVAL 30 DAY
    AND st.strategy_id IS NULL;
```

---

# 七、数据流与入库要点（执行层面）

1. **监听更新通知** → 拉取 groups/strategy/userGroup 三类全量文件 → 记录 `file_ingest`。
2. **校验 QC/MD5**：计算 .txt 的MD5 与 `md5sum` 对比，置 `verified=1`。
3. **群**：upsert 到 `user_group`，并将每次生成写入 `user_group_snapshot`。
4. **策略**：upsert `strategy`；将 GroupArray 写入 `strategy_group`，并反补 `user_group` 的名称/口径（如有更新）。
5. **用户-群**：解析 userGroup.txt 的 GroupList，批量 upsert 到 `user_group_membership`，并更新 `last_seen_at`。

---

如果你希望，我可以把上述 DDL 打包成一份可直接执行的 **.sql** 文件，或者根据你们现有库名/表前缀再做一次重命名适配。需要我顺手出一份“样例入库脚本（从三类CSV/TXT导入）”吗？
