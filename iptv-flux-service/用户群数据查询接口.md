好的，我来帮你设计一个 **用户群数据查询接口**，结合你给的 MySQL 表结构（`user_group` + `user_group_snapshot`）和文档业务规则。

---

## 1. 需求分析

**已有必选参数**

* **平台标识**（Platform）

  * 必填，字符串，可多值（如“可视化平台、精准营销平台”）。

**已有可选参数**

* **运营商**（operator\_code）

  * 非必填，可选值：dx=电信 / lt=联通 / yd=移动。

**建议增加的可选参数**（便于精确查询）

1. **群ID / 群名称**（group\_id / group\_name）

   * 支持精确或模糊查询。
2. **覆盖用户数区间**（coverage\_min / coverage\_max）

   * 根据业务有时需要筛选大群、小群。
3. **生成时间范围**（generate\_time\_start / generate\_time\_end）

   * 查询某段时间内生成的群文件（配合快照表）。
4. **策略ID**（strategy\_id）

   * 如果要查某策略绑定的群。
5. **是否只查最新快照**（latest\_only）

   * 默认true，减少重复历史记录。

---

## 2. API 设计

### 2.1 接口定义

* **URL**: `/api/user-groups`
* **Method**: `GET`
* **Query Parameters**:

| 参数名                   | 类型       | 必填 | 说明              |
| --------------------- | -------- | -- | --------------- |
| platform              | string   | ✅  | 平台标识（可多值，用逗号分隔） |
| operator\_code        | string   | ❌  | 运营商代码（dx/lt/yd） |
| group\_id             | string   | ❌  | 群ID（支持模糊）       |
| group\_name           | string   | ❌  | 群名称（模糊匹配）       |
| coverage\_min         | int      | ❌  | 覆盖用户数下限         |
| coverage\_max         | int      | ❌  | 覆盖用户数上限         |
| generate\_time\_start | datetime | ❌  | 生成时间起           |
| generate\_time\_end   | datetime | ❌  | 生成时间止           |
| strategy\_id          | string   | ❌  | 限定策略ID绑定的群      |
| latest\_only          | boolean  | ❌  | 仅查最新快照，默认true   |
| page                  | int      | ❌  | 页码（默认1）         |
| page\_size            | int      | ❌  | 每页数量（默认50）      |

---

### 2.2 MySQL 查询逻辑

接口查询结果来源于 **user\_group**（群元数据）+ **user\_group\_snapshot**（文件快照），必要时可关联 `strategy_group`。

#### 查询示例（latest\_only = true）

```sql
SELECT 
    g.group_id,
    g.group_name,
    g.platform,
    g.operator_code,
    s.coverage,
    s.file_url,
    s.generate_time,
    s.md5sum
FROM user_group g
JOIN (
    SELECT us.group_id, us.coverage, us.file_url, us.generate_time, us.md5sum
    FROM user_group_snapshot us
    JOIN (
        SELECT group_id, MAX(generate_time) AS max_gen
        FROM user_group_snapshot
        GROUP BY group_id
    ) t ON us.group_id = t.group_id AND us.generate_time = t.max_gen
) s ON g.group_id = s.group_id
/*** 筛选条件 ***/
WHERE FIND_IN_SET(:platform, g.platform)
  AND (:operator_code IS NULL OR g.operator_code = :operator_code)
  AND (:group_id IS NULL OR g.group_id LIKE CONCAT('%', :group_id, '%'))
  AND (:group_name IS NULL OR g.group_name LIKE CONCAT('%', :group_name, '%'))
  AND (:coverage_min IS NULL OR s.coverage >= :coverage_min)
  AND (:coverage_max IS NULL OR s.coverage <= :coverage_max)
  AND (:generate_time_start IS NULL OR s.generate_time >= :generate_time_start)
  AND (:generate_time_end IS NULL OR s.generate_time <= :generate_time_end)
  AND (:strategy_id IS NULL OR EXISTS (
        SELECT 1 FROM strategy_group sg
        WHERE sg.group_id = g.group_id
          AND sg.strategy_id = :strategy_id
     ))
ORDER BY s.generate_time DESC
LIMIT :offset, :page_size;
```

#### 查询示例（latest\_only = false）

```sql
SELECT 
    g.group_id,
    g.group_name,
    g.platform,
    g.operator_code,
    s.coverage,
    s.file_url,
    s.generate_time,
    s.md5sum
FROM user_group g
JOIN user_group_snapshot s
  ON g.group_id = s.group_id
WHERE FIND_IN_SET(:platform, g.platform)
  /* 其他条件同上 */
ORDER BY s.generate_time DESC
LIMIT :offset, :page_size;
```

---

### 2.3 返回示例

```json
{
  "page": 1,
  "page_size": 50,
  "total": 2,
  "data": [
    {
      "group_id": "CG00000053",
      "group_name": "积分特惠分组",
      "platform": "可视化平台",
      "operator_code": "dx",
      "coverage": 361009,
      "file_url": "*************************************************/CG00000053.txt",
      "generate_time": "2025-07-31T11:02:15",
      "md5sum": "ex6f5902ac237024bdd0c176cb93063d"
    }
  ]
}
```

---

### 2.4 其他建议

* **安全性**：返回文件URL时可加访问签名或通过代理下载，避免暴露FTP账号密码。
* **性能优化**：

  * 对 `platform` 建全文索引或 JSON 索引（如平台改为JSON数组存储）。
  * 对 `generate_time` 建索引，加快时间范围查询。
* **扩展性**：

  * 未来可增加“覆盖用户数TOP N 群”快速查询。
  * 可支持多平台、多运营商批量筛选。

---

如果你需要的话，我可以帮你直接**写好一个可运行的 Spring Boot Controller + MyBatis 查询实现**，这样你的开发可以直接用代码跑这个接口。
你要我帮你写实现代码吗？
