spring:
  application:
    name: @project.artifactId@
    deleteBeforeInit: true
  profiles:
    active: @profiles.active@
  main:
    allow-bean-definition-overriding: true
  config:
    allow-override: true     # 允许nacos被本地文件覆盖
    override-none: true     # nacos不覆盖任何本地文件
    override-system-properties: true   # nacos 覆盖系统属性。注意本地配置文件不是系统属性
  cloud:
    nacos:
      discovery:
        metadata:
          management:
            context-path: ${server.servlet.context-path:}/actuator
        server-addr: ${nacos.server-addr}
        username: ${nacos.username}
        password: ${nacos.password}
        namespace: ${nacos.namespace}
        group: ${nacos.group}
        enabled: true
      config:
        server-addr: ${nacos.server-addr}
        username: ${nacos.username}
        password: ${nacos.password}
        file-extension: ${nacos.file-extension}
        namespace: ${nacos.namespace}
        group: ${nacos.group}
        refresh-enabled: true
        extension-configs:
          - data-id: ${spring.application.name}-${spring.profiles.active}.${nacos.file-extension}
            group: ${nacos.group}
            refresh: true
        shared-configs:
          - data-id: iptv-redis-${spring.profiles.active}.${nacos.file-extension}
            group: ${nacos.group}
            refresh: true
          - data-id: iptv-mysql-${spring.profiles.active}.${nacos.file-extension}
            group: ${nacos.group}
            refresh: true
# 日志配置
log:
  file:
    path: /iptv/bokong/data/applogs/${spring.application.name}/
    name: ${spring.application.name}
    suffix: log
logging:
  level:
    root: INFO
    com.iptv.flux: INFO
    org.springframework: WARN
    org.springframework.cloud.gateway: WARN
    org.springframework.cloud.nacos: WARN
    org.springframework.boot: WARN
    org.springframework.context: WARN
    org.springframework.beans: WARN
    com.alibaba.nacos: WARN
    org.springframework.data.redis: WARN
    org.redisson: WARN
    com.zaxxer.hikari: WARN
    org.jooq: WARN

# 异步处理配置
async:
  data-process:
    core-pool-size: 4
    max-pool-size: 8
    queue-capacity: 100
  file-download:
    core-pool-size: 8
    max-pool-size: 16
    queue-capacity: 200
  task:
    timeout: 300000  # 5分钟任务超时
    max-retry: 3     # 最大重试次数

# FTP配置
ftp:
  download:
    temp-dir: /tmp/ftp-downloads
    timeout: 30000