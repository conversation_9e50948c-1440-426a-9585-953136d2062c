server:
  port: ${SERVER_PORT:7003}
  #servlet:
  #  context-path: /@project.artifactId@
#nacos服务地址及账号密码
nacos:
  server-addr: **************:8848
  namespace: bokong_dev
  username: nacos
  password: LCW6KLEAHa_vectvznfv
  #password: nacos
  group: iptv
  file-extension: yaml

# 本地配置（可被nacos覆盖）
user-group:
  blackwhitelist:
    enabled: false  # 默认关闭，可控制开启
    cache:
      ttl: 3600     # 黑白名单缓存1小时

# 异步处理配置
async:
  data-process:
    core-pool-size: 2  # 开发环境减少线程数
    max-pool-size: 4
    queue-capacity: 50
  file-download:
    core-pool-size: 4
    max-pool-size: 8
    queue-capacity: 100
  task:
    timeout: 180000  # 开发环境3分钟超时
    max-retry: 2     # 开发环境减少重试次数

# FTP配置
ftp:
  download:
    temp-dir: /tmp/ftp-downloads-dev
    timeout: 20000