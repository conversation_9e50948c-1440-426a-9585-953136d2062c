package com.iptv.flux.service.service;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.UserGroupDTO;
import com.iptv.flux.common.utils.CacheKeyBuilder;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.enums.BloomFilterMode;
import com.iptv.flux.service.model.entity.UserGroupRelation;
import com.iptv.flux.service.repository.UserGroupRepository;
import com.iptv.flux.service.security.AccessPatternDetector;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.micrometer.core.annotation.Timed;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBloomFilter;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.service
 * @className: UserGroupService
 * @author: chiron
 * @description: TODO
 * @date: 2025/3/4 12:59
 * @version: 1.0
 */
@Service
@Slf4j
public class UserGroupService {
    private final StringRedisTemplate redisTemplate;
    private final RedissonClient redisson;
    private final RBloomFilter<String> bloomFilter;
    private final ExecutorService asyncExecutor;
    private final Cache<String, Set<String>> localCache;
    private final MetricsRegistryUtil metricsRegistry;
    private final UserGroupRepository userGroupRepository;
    private final CircuitBreaker circuitBreaker;
    // 新增注入
    private final BlacklistService blacklistService;
    private final WhitelistService whitelistService;

    // 新增配置开关
    @Value("${user-group.blackwhitelist.enabled:false}")
    private boolean blackWhiteListEnabled;

    @Value("${user-group.blackwhitelist.cache.ttl:3600}")
    private long blackWhiteListCacheTtl;


    @Value("${user-group.bloom-filter.mode:MONITOR_ONLY}")
    private BloomFilterMode bloomFilterMode;

    @Autowired
    private AccessPatternDetector accessPatternDetector;

    @Autowired
    private LoadingCache<String, Boolean> knownMissingKeysCache;

    @Autowired
    private RateLimiter cachePenetrationRateLimiter;

    @Value("${user-group.redis.ttl:43200}")
    private long redisTtl;

    public UserGroupService(
            @Qualifier("stringRedisTemplate") StringRedisTemplate redisTemplate,
            RedissonClient redisson,
            RBloomFilter<String> bloomFilter,
            @Qualifier(UserGroupConstants.ASYNC_EXECUTOR_BEAN) ExecutorService asyncExecutor,
            Cache<String, Set<String>> localCache,
            MetricsRegistryUtil metricsRegistry,
            UserGroupRepository userGroupRepository,
            CircuitBreakerRegistry circuitBreakerRegistry, BlacklistService blacklistService, WhitelistService whitelistService, AccessPatternDetector accessPatternDetector) {

        this.redisTemplate = redisTemplate;
        this.redisson = redisson;
        this.bloomFilter = bloomFilter;
        this.asyncExecutor = asyncExecutor;
        this.localCache = localCache;
        this.metricsRegistry = metricsRegistry;
        this.userGroupRepository = userGroupRepository;
        this.circuitBreaker = circuitBreakerRegistry.circuitBreaker(UserGroupConstants.CIRCUIT_BREAKER_NAME);
        this.blacklistService = blacklistService;
        this.whitelistService = whitelistService;
        this.accessPatternDetector = accessPatternDetector;

        log.info("流量平台-----> UserGroupService初始化完成，Redis TTL: {}秒，黑白名单功能: {}",
                redisTtl, blackWhiteListEnabled ? "启用" : "禁用");
    }

    @PostConstruct
    public void init() {
        log.info("流量平台-----> 正在初始化UserGroupService...");
        metricsRegistry.createTimer(UserGroupConstants.DB_QUERY_TIMER);
        metricsRegistry.createCounter(UserGroupConstants.CACHE_HIT_METRIC);
        metricsRegistry.createCounter(UserGroupConstants.REDIS_HIT_METRIC);
        metricsRegistry.createCounter(UserGroupConstants.CACHE_MISS_METRIC);
        metricsRegistry.createCounter(UserGroupConstants.QUERY_TOTAL_METRIC);
        metricsRegistry.createCounter(UserGroupConstants.QUERY_ERROR_METRIC);
        metricsRegistry.createCounter(UserGroupConstants.BLOOM_FILTER_HIT_METRIC);

        // 注册黑白名单监控指标
        metricsRegistry.createCounter(UserGroupConstants.BLACKWHITELIST_CACHE_HIT_METRIC);
        metricsRegistry.createCounter(UserGroupConstants.BLACKWHITELIST_BLACKLIST_HIT_METRIC);
        metricsRegistry.createCounter(UserGroupConstants.BLACKWHITELIST_WHITELIST_HIT_METRIC);
        metricsRegistry.createCounter(UserGroupConstants.BLACKWHITELIST_CHECK_ERROR_METRIC);

        // 注册缓存统计
        metricsRegistry.createGauge("user.group.local.cache.size", localCache, Cache::estimatedSize);
        metricsRegistry.createGauge("user.group.bloom.filter.size", bloomFilter, RBloomFilter::count);
        log.info("流量平台-----> UserGroupService指标注册完成");
    }

    /**
     * 加载用户分组信息
     *
     * @param source 来源标识
     * @param userId 用户ID
     * @return 分组ID集合
     */
    @Timed(value = "service.usergroup.load", percentiles = {0.5, 0.95, 0.99})
    public Set<String> loadUserGroupInfo(String source, String userId, String clientIp) {
        final String compositeKey = CacheKeyBuilder.buildUserGroupCompositeKey(source, userId);
        log.debug("流量平台-----> 正在为键: {} 加载用户分组信息", compositeKey);

        metricsRegistry.incrementCounter(UserGroupConstants.QUERY_TOTAL_METRIC);

        // 新增：黑白名单检查（如果启用）
        if (blackWhiteListEnabled) {
            Set<String> blackWhiteListResult = checkBlackWhiteList(compositeKey, userId, source);
            if (blackWhiteListResult != null) {
                return blackWhiteListResult;
            }
        }
        return circuitBreaker.executeSupplier(() -> processWithCache(compositeKey, userId, source, clientIp));
    }

    private Set<String> processWithCache(String compositeKey, String userId, String source, String clientIp) {
        // 1. 检查本地缓存
        Set<String> groups = getFromLocalCache(compositeKey);
        if (groups != null) {
            return groups;
        }
        // 2. 检查已知不存在的键缓存
        if (knownMissingKeysCache.getIfPresent(compositeKey) != null &&
                Boolean.TRUE.equals(knownMissingKeysCache.getIfPresent(compositeKey))) {
            log.debug("流量平台-----> 键已知不存在: {}", compositeKey);
            return Collections.emptySet();
        }
        // 3. 访问Redis和数据库
        return getFromRedisOrDb(compositeKey, userId, source, clientIp);
    }

    /**
     * 从本地缓存获取数据
     * @param key
     * @return
     */
    private Set<String> getFromLocalCache(String key) {
        long startTime = System.currentTimeMillis();
        Set<String> groups = localCache.getIfPresent(key);

        if (groups != null) {
            metricsRegistry.incrementCounter(UserGroupConstants.CACHE_HIT_METRIC);
            log.debug("流量平台-----> 本地缓存命中键: {}, 耗时 {}ms", key, System.currentTimeMillis() - startTime);
            metricsRegistry.recordExecutionTime("cache.local.hit.time", startTime);
        }

        return groups;
    }


    private Set<String> getFromRedisOrDb(String compositeKey, String userId, String source, String clientIp) {
        // 1. 首先尝试从Redis获取数据，无论布隆过滤器的判断结果如何
        String redisKey = CacheKeyBuilder.buildUserGroupRedisKey(compositeKey);
        Set<String> groups = queryRedisWithFallback(redisKey, compositeKey);

        if (!CollectionUtils.isEmpty(groups)) {
            metricsRegistry.incrementCounter(UserGroupConstants.REDIS_HIT_METRIC);
            // 更新本地缓存
            localCache.put(compositeKey, groups);
            log.debug("流量平台-----> Redis缓存命中键: {}, 找到 {} 个分组", compositeKey, groups.size());
            return groups;
        }

        // 2. 处理布隆过滤器逻辑 - 根据配置决定是否使用布隆过滤器拦截
        if (bloomFilterMode != BloomFilterMode.DISABLED) {
            boolean isSuspicious = accessPatternDetector.isSuspicious(compositeKey, clientIp);

            // 检查布隆过滤器
            if (!bloomFilter.contains(compositeKey)) {
                if (bloomFilterMode == BloomFilterMode.PROTECTION && isSuspicious) {
                    // 防护模式：拦截可疑请求
                    try {
                        // 对可疑请求应用限流
                        cachePenetrationRateLimiter.acquirePermission();
                    } catch (Exception e) {
                        log.warn("流量平台-----> 对可疑访问键: {}, IP: {} 进行限流", compositeKey, clientIp);
                        metricsRegistry.incrementCounter(UserGroupConstants.BLOOM_FILTER_HIT_METRIC);
                        return Collections.emptySet();
                    }

                    // 即使通过了限流，仍然记录可能的缓存穿透
                    metricsRegistry.incrementCounter(UserGroupConstants.BLOOM_FILTER_HIT_METRIC);
                    log.warn("流量平台-----> 检测到潜在的缓存穿透但已允许(键: {}, IP: {})",
                            compositeKey, clientIp);
                } else if (bloomFilterMode == BloomFilterMode.MONITOR_ONLY) {
                    // 监控模式：记录但不阻止
                    if (isSuspicious) {
                        metricsRegistry.incrementCounter("bloom.filter.potential.bypass");
                        log.warn("流量平台-----> 检测到潜在的缓存穿透(仅监控): {}", compositeKey);
                    }
                }
            }
        }

        // 3. 查询数据库
        Set<String> result = queryUnderLock(redisKey, compositeKey, userId, source);

        // 4. 如果数据库也没有数据，记录到已知不存在键缓存
        if (result.isEmpty()) {
            knownMissingKeysCache.put(compositeKey, true);
        } else {
            // 数据存在但布隆过滤器中没有，自动添加到布隆过滤器
            if (!bloomFilter.contains(compositeKey)) {
                bloomFilter.add(compositeKey);
                log.info("流量平台-----> 自动完成布隆过滤器键: {}", compositeKey);
                metricsRegistry.incrementCounter(UserGroupConstants.BLOOM_FILTER_AUTO_COMPLETE_METRIC);
            }
        }

        return result;
    }

    /**
     * 查询Redis并处理可能的异常
     * @param redisKey
     * @param field
     * @return
     */
    private Set<String> queryRedisWithFallback(String redisKey, String field) {
        long startTime = System.currentTimeMillis();
        try {
            HashOperations<String, String, String> hashOps = redisTemplate.opsForHash();
            String value = hashOps.get(redisKey, field);
            Set<String> result = deserializeGroups(value);

            log.debug("流量平台-----> Redis查询键: {}, 字段: {}, 找到: {}, 耗时 {}ms",
                    redisKey, field, !result.isEmpty(), System.currentTimeMillis() - startTime);

            return result;
        } catch (Exception e) {
            log.error("流量平台-----> Redis查询失败, 键: {}, 字段: {}", redisKey, field, e);
            metricsRegistry.incrementCounter("redis.error");
            return Collections.emptySet();
        }
    }

    /**
     * 查询数据库并处理锁逻辑
     * @param redisKey
     * @param compositeKey
     * @param userId
     * @param source
     * @return
     */
    private Set<String> queryUnderLock(String redisKey, String compositeKey, String userId, String source) {
        final String lockKey = CacheKeyBuilder.buildLockKey(redisKey);
        final RLock lock = redisson.getLock(lockKey);

        log.debug("流量平台-----> 正在获取锁: {}", lockKey);

        try {
            // 尝试获取锁并设置超时时间
            if (lock.tryLock(UserGroupConstants.REDIS_LOCK_WAIT_MS,
                    UserGroupConstants.REDIS_LOCK_LEASE_MS,
                    TimeUnit.MILLISECONDS)) {

                return handleLockAcquired(redisKey, compositeKey, userId, source, lock);
            }

            log.warn("流量平台-----> 获取锁失败: {}", lockKey);
            metricsRegistry.incrementCounter("lock.acquisition.failure");
            return Collections.emptySet();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("流量平台-----> 锁请求被中断: {}", lockKey, e);
            metricsRegistry.incrementCounter("lock.interrupted");
            return Collections.emptySet();
        } finally {
            tryUnlock(lock);
        }
    }

    /**
     * 尝试释放锁
     * @param lock
     */
    private void tryUnlock(RLock lock) {
        if (lock.isHeldByCurrentThread()) {
            try {
                lock.unlock();
                log.debug("流量平台-----> 锁已释放");
            } catch (Exception e) {
                log.error("流量平台-----> 解锁错误: {}", e.getMessage());
            }
        }
    }

    /**
     * 处理锁已获取
     * @param redisKey
     * @param compositeKey
     * @param userId
     * @param source
     * @param lock
     * @return
     */
    private Set<String> handleLockAcquired(String redisKey, String compositeKey, String userId, String source, RLock lock) {
        log.debug("流量平台-----> 已获取锁: {}", lock.getName());

        // 在获取锁后，请再次检查Redis
        Set<String> groups = queryRedisWithFallback(redisKey, compositeKey);
        if (!groups.isEmpty()) {
            localCache.put(compositeKey, groups);
            log.debug("流量平台-----> 锁后再次检查Redis: {} 找到数据", compositeKey);
            return groups;
        }

        // 查询数据库使用指标
        groups = metricsRegistry.recordTimerMetric(
                UserGroupConstants.DB_QUERY_TIMER,
                () -> {
                    try {
                        long startTime = System.currentTimeMillis();
                        Set<String> result = userGroupRepository.queryGroupsByUserId(userId, source);
                        log.debug("流量平台-----> 数据库查询用户ID: {}, 来源: {}, 找到: {}, 耗时 {}ms",
                                userId, source, !result.isEmpty(), System.currentTimeMillis() - startTime);
                        return result;
                    } catch (Exception e) {
                        log.error("流量平台-----> 无效的用户ID格式: {}", userId, e);
                        metricsRegistry.incrementCounter(UserGroupConstants.INVALID_USER_ID_METRIC);
                        return Collections.emptySet();
                    }
                });

        if (!groups.isEmpty()) {
            // 异步更新缓存
            asyncUpdateCache(redisKey, compositeKey, groups);
        } else {
            handleEmptyResult(redisKey, compositeKey);
        }

        return groups;
    }

    /**
     * 异步更新缓存
     * @param redisKey
     * @param compositeKey
     * @param groups
     */
    private void asyncUpdateCache(String redisKey, String compositeKey, Set<String> groups) {
        CompletableFuture.runAsync(() -> {
            try {
                log.debug("流量平台-----> 异步更新缓存键: {}", compositeKey);

                // 将序列化组添加到Redis
                redisTemplate.opsForHash().put(redisKey, compositeKey, serializeGroups(groups));

                // 添加带有抖动的过期时间以避免缓存雪崩
                long jitter = ThreadLocalRandom.current().nextLong(0, 300);
                redisTemplate.expire(redisKey, Duration.ofSeconds(redisTtl + jitter));
                bloomFilter.add(compositeKey);
                localCache.put(compositeKey, groups);

                log.debug("流量平台-----> 缓存成功更新键: {}, 分组数: {}", compositeKey, groups.size());
            } catch (Exception e) {
                log.error("流量平台-----> 异步缓存更新失败，键: {}", compositeKey, e);
                metricsRegistry.incrementCounter("cache.update.error");
            }
        }, asyncExecutor);
    }

    /**
     * 处理空结果
     * @param redisKey
     * @param compositeKey
     */
    private void handleEmptyResult(String redisKey, String compositeKey) {
        try {
            log.debug("流量平台-----> 处理空结果，键: {}", compositeKey);

            bloomFilter.add(compositeKey);
            redisTemplate.opsForHash().put(redisKey, compositeKey, UserGroupConstants.EMPTY_RESULT);
            redisTemplate.expire(redisKey, Duration.ofSeconds(
                    ThreadLocalRandom.current().nextLong(300, 600)));

            log.debug("流量平台-----> 空结果已缓存，键: {}", compositeKey);
        } catch (Exception e) {
            log.error("流量平台-----> 处理空结果失败，键: {}", compositeKey, e);
            metricsRegistry.incrementCounter("empty.result.handling.error");
        }
    }

    /**
     * 序列化和反序列化分组信息
     * @param groups
     * @return
     */
    private String serializeGroups(Set<String> groups) {
        return String.join(",", groups);
    }

    /**
     * 反序列化分组信息
     * @param value
     * @return
     */
    private Set<String> deserializeGroups(String value) {
        if (value == null || Objects.equals(value, UserGroupConstants.EMPTY_RESULT)) {
            return Collections.emptySet();
        }

        return Collections.unmodifiableSet(new LinkedHashSet<>(Arrays.asList(value.split(","))));
    }

    /**
     * 保存用户分组信息
     *
     * @param userGroupDTO 用户分组数据
     */
    @Timed(value = "service.usergroup.save", percentiles = {0.5, 0.95, 0.99})
    public void saveUserGroupInfo(UserGroupDTO userGroupDTO) {
        long startTime = System.currentTimeMillis();

        String userId = userGroupDTO.getUserId();
        String source = userGroupDTO.getSource();
        Set<String> groupIds = userGroupDTO.getGroupIds();

        log.info("流量平台-----> 正在保存用户ID: {}, 来源: {}, 分组数: {} 的用户分组信息",
                userId, source, groupIds.size());

        try {
            // 保存到数据库
            userGroupRepository.saveUserGroupRelation(
                    UserGroupRelation.builder()
                            .userId(userId)
                            .source(source)
                            .groupIds(serializeGroups(groupIds))
                            .build()
            );

            // 更新缓存
            String compositeKey = CacheKeyBuilder.buildUserGroupCompositeKey(source, userId);
            String redisKey = CacheKeyBuilder.buildUserGroupRedisKey(compositeKey);
            asyncUpdateCache(redisKey, compositeKey, groupIds);

            log.debug("流量平台-----> 用户ID: {}, 来源: {} 的用户分组信息已保存，耗时 {}ms",
                    userId, source, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("流量平台-----> 保存用户ID: {}, 来源: {} 的用户分组信息出错", userId, source, e);
            metricsRegistry.incrementCounter("usergroup.save.error");
            throw e;
        }
    }

    /**
     * 关闭服务
     */
    @PreDestroy
    public void shutdown() {
        log.info("流量平台-----> 正在关闭UserGroupService...");
        if (asyncExecutor != null && !asyncExecutor.isShutdown()) {
            asyncExecutor.shutdown();
            try {
                if (!asyncExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    log.warn("流量平台-----> 强制关闭执行器...");
                    asyncExecutor.shutdownNow();
                }
                log.info("流量平台-----> 执行器已完全关闭");
            } catch (InterruptedException e) {
                log.error("流量平台-----> 执行器关闭被中断", e);
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 预加载用户分组信息到缓存中
     * 仅供缓存预热使用
     */
    public void preloadUserGroupInfo(String redisKey, String compositeKey, Set<String> groupIds) {
        if (groupIds == null || groupIds.isEmpty()) {
            handleEmptyResult(redisKey, compositeKey);
            return;
        }

        try {
            log.debug("流量平台-----> 正在预加载键: {} 的缓存", compositeKey);

            // 添加到Redis
            redisTemplate.opsForHash().put(redisKey, compositeKey, serializeGroups(groupIds));

            // 添加过期时间
            long jitter = ThreadLocalRandom.current().nextLong(0, 300);
            redisTemplate.expire(redisKey, Duration.ofSeconds(redisTtl + jitter));

            // 添加到布隆过滤器
            bloomFilter.add(compositeKey);

            // 添加到本地缓存
            localCache.put(compositeKey, groupIds);

            log.debug("流量平台-----> 键: {} 的缓存预加载成功，分组数: {}", compositeKey, groupIds.size());
        } catch (Exception e) {
            log.error("流量平台-----> 键: {} 的缓存预加载失败", compositeKey, e);
            metricsRegistry.incrementCounter("cache.preload.error");
        }
    }

    /**
     * 获取用户分组列表（分页）
     *
     * @param source   请求来源（可选）
     * @param userId   用户ID（可选）
     * @param page     页码
     * @param pageSize 每页大小
     * @return 用户分组列表和分页信息
     */
    @Timed(value = "service.usergroup.list", percentiles = {0.5, 0.95, 0.99})
    public Map<String, Object> getUserGroupList(String source, String userId, int page, int pageSize) {
        long startTime = System.currentTimeMillis();

        log.debug("流量平台-----> 查询用户分组列表，来源: {}, 用户ID: {}, 页码: {}, 每页大小: {}",
                source, userId, page, pageSize);

        int offset = (page - 1) * pageSize;

        // 查询总数和分页数据
        long total = userGroupRepository.countUserGroups(source, userId);
        List<UserGroupRelation> relations = userGroupRepository.findUserGroupsWithPagination(source, userId, offset, pageSize);

        // 转换为前端需要的格式
        List<Map<String, Object>> items = new ArrayList<>();
        for (UserGroupRelation relation : relations) {
            Map<String, Object> item = new HashMap<>();
            item.put("id", relation.getId());
            item.put("userId", relation.getUserId());
            item.put("source", relation.getSource());

            // 将分组IDs转换为List
            String groupIdsStr = relation.getGroupIds();
            List<String> groupIdsList = new ArrayList<>();
            if (groupIdsStr != null && !groupIdsStr.isEmpty()) {
                groupIdsList = Arrays.asList(groupIdsStr.split(","));
            }
            item.put("groupIds", groupIdsList);

            // 获取创建和更新时间
            item.put("createdAt", relation.getCreatedAt());
            item.put("updatedAt", relation.getUpdatedAt());

            items.add(item);
        }

        // 构建结果
        Map<String, Object> result = new HashMap<>();
        result.put("items", items);
        result.put("total", total);
        result.put("page", page);
        result.put("pageSize", pageSize);
        result.put("totalPages", (total + pageSize - 1) / pageSize);

        log.debug("流量平台-----> 查询用户分组列表完成，共 {} 条记录，耗时 {}ms",
                items.size(), System.currentTimeMillis() - startTime);

        return result;
    }

    /**
     * 删除用户分组
     *
     * @param id 用户分组ID
     */
    @Timed(value = "service.usergroup.delete", percentiles = {0.5, 0.95, 0.99})
    public void deleteUserGroup(Long id) {
        long startTime = System.currentTimeMillis();

        log.info("流量平台-----> 正在删除用户分组，ID: {}", id);

        // 先查询获取数据，以便更新缓存
        UserGroupRelation relation = userGroupRepository.findById(id);
        if (relation == null) {
            log.warn("流量平台-----> 要删除的用户分组不存在，ID: {}", id);
            return;
        }

        // 从数据库删除
        userGroupRepository.deleteUserGroup(id);

        // 清除相关缓存
        String compositeKey = CacheKeyBuilder.buildUserGroupCompositeKey(relation.getSource(), relation.getUserId());
        String redisKey = CacheKeyBuilder.buildUserGroupRedisKey(compositeKey);

        // 异步清除缓存
        CompletableFuture.runAsync(() -> {
            try {
                // 清除Redis缓存
                redisTemplate.opsForHash().delete(redisKey, compositeKey);
                // 清除本地缓存
                localCache.invalidate(compositeKey);
                log.debug("流量平台-----> 已清除用户分组缓存，键: {}", compositeKey);
            } catch (Exception e) {
                log.error("流量平台-----> 清除用户分组缓存失败，键: {}", compositeKey, e);
                metricsRegistry.incrementCounter("cache.clear.error");
            }
        }, asyncExecutor);

        log.info("流量平台-----> 用户分组删除完成，ID: {}，耗时 {}ms",
                id, System.currentTimeMillis() - startTime);
    }

    /**
     * 黑白名单检查逻辑
     * @return null表示不在黑白名单中，继续正常流程；非null表示直接返回结果
     */
    private Set<String> checkBlackWhiteList(String compositeKey, String userId, String source) {
        try {
            // 1. 先检查黑白名单缓存
            Set<String> cachedResult = getBlackWhiteListFromCache(compositeKey);
            if (cachedResult != null) {
                log.debug("流量平台-----> 黑白名单缓存命中: {}", compositeKey);
                metricsRegistry.incrementCounter(UserGroupConstants.BLACKWHITELIST_CACHE_HIT_METRIC);
                return cachedResult;
            }

            // 2. 检查黑名单
            if (blacklistService.isBlacklisted(userId, source)) {
                log.info("流量平台-----> 用户在黑名单中: {}, 来源: {}", userId, source);
                Set<String> emptyResult = Collections.emptySet();
                cacheBlackWhiteListResult(compositeKey, emptyResult, "BLACKLIST");
                metricsRegistry.incrementCounter(UserGroupConstants.BLACKWHITELIST_BLACKLIST_HIT_METRIC);
                return emptyResult;
            }

            // 3. 检查白名单
            if (whitelistService.isWhitelisted(userId, source)) {
                log.info("流量平台-----> 用户在白名单中: {}, 来源: {}", userId, source);
                // 白名单用户需要查询其实际分组
                Set<String> whitelistGroups = queryWhitelistUserGroups(userId, source);
                cacheBlackWhiteListResult(compositeKey, whitelistGroups, "WHITELIST");
                metricsRegistry.incrementCounter(UserGroupConstants.BLACKWHITELIST_WHITELIST_HIT_METRIC);
                return whitelistGroups;
            }

            // 4. 不在黑白名单中，返回null继续正常流程
            return null;

        } catch (Exception e) {
            log.error("流量平台-----> 黑白名单检查失败: {}, 来源: {}", userId, source, e);
            metricsRegistry.incrementCounter(UserGroupConstants.BLACKWHITELIST_CHECK_ERROR_METRIC);
            // 出错时继续正常流程，确保服务可用性
            return null;
        }
    }

    /**
     * 从缓存获取黑白名单结果
     */
    private Set<String> getBlackWhiteListFromCache(String compositeKey) {
        try {
            // 使用不同的前缀避免冲突
            String cacheKey = "bwl:" + compositeKey;
            String cachedValue = redisTemplate.opsForValue().get(cacheKey);

            if (cachedValue != null) {
                if ("BLACKLIST".equals(cachedValue)) {
                    return Collections.emptySet();
                } else if (cachedValue.startsWith("WHITELIST:")) {
                    String groupsStr = cachedValue.substring("WHITELIST:".length());
                    return deserializeGroups(groupsStr);
                }
            }
        } catch (Exception e) {
            log.warn("流量平台-----> 获取黑白名单缓存失败: {}", compositeKey, e);
        }
        return null;
    }

    /**
     * 缓存黑白名单结果
     */
    private void cacheBlackWhiteListResult(String compositeKey, Set<String> groups, String type) {
        try {
            String cacheKey = "bwl:" + compositeKey;
            String cacheValue;

            if ("BLACKLIST".equals(type)) {
                cacheValue = "BLACKLIST";
            } else {
                cacheValue = "WHITELIST:" + serializeGroups(groups);
            }

            redisTemplate.opsForValue().set(cacheKey, cacheValue, Duration.ofSeconds(blackWhiteListCacheTtl));
            log.debug("流量平台-----> 黑白名单结果已缓存: {}, 类型: {}", compositeKey, type);
        } catch (Exception e) {
            log.warn("流量平台-----> 缓存黑白名单结果失败: {}", compositeKey, e);
        }
    }

    /**
     * 查询白名单用户的分组
     */
    private Set<String> queryWhitelistUserGroups(String userId, String source) {
        // 白名单用户直接查询数据库获取其分组
        return userGroupRepository.queryGroupsByUserId(userId, source);
    }
}