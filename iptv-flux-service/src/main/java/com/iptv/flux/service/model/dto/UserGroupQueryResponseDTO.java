package com.iptv.flux.service.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户分组查询响应DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户分组查询响应")
public class UserGroupQueryResponseDTO {

    @Schema(description = "分组ID", example = "GROUP_001")
    private String groupId;

    @Schema(description = "分组名称", example = "高价值用户群")
    private String groupName;

    @Schema(description = "平台标识", example = "集约平台")
    private String platform;

    @Schema(description = "来源标识（运营商代码）", example = "dx")
    private String source;

    @Schema(description = "覆盖度（用户数量）", example = "50000")
    private Long coverage;

    @Schema(description = "文件URL", example = "ftp://***********/data/dx_users.txt")
    private String fileUrl;

    @Schema(description = "质检文件URL", example = "ftp://***********/data/dx_users_qc.txt")
    private String qcFileUrl;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "生成时间", example = "2024-01-01 12:00:00")
    private LocalDateTime generateTime;

    @Schema(description = "生成时间原始格式", example = "20240101120000")
    private String generateTimeRaw;

    @Schema(description = "MD5校验值", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String md5sum;

    @Schema(description = "是否已验证", example = "true")
    private Boolean verified;

    @Schema(description = "策略ID", example = "STRATEGY_001")
    private String strategyId;

    @Schema(description = "策略名称", example = "高价值用户识别策略")
    private String strategyName;

    @Schema(description = "分组描述")
    private String description;

    @Schema(description = "创建时间", example = "2024-01-01 12:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
}
