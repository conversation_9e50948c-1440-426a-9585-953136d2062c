package com.iptv.flux.service.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 统一的列表管理DTO
 * 用于黑名单和白名单的统一管理
 * 
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "统一列表管理请求")
public class UnifiedListManagementDTO {

    @NotBlank(message = "列表类型不能为空")
    @Pattern(regexp = "^(blacklist|whitelist)$", message = "列表类型只能是 blacklist 或 whitelist")
    @Schema(description = "列表类型", example = "blacklist", allowableValues = {"blacklist", "whitelist"})
    private String listType;

    @Schema(description = "用户ID", example = "user123")
    private String userId;

    @Schema(description = "来源", example = "dx", allowableValues = {"dx", "yd", "lt"})
    private String source;

    @Schema(description = "备注", example = "恶意用户")
    private String remark;

    @Schema(description = "记录ID（用于删除操作）", example = "1")
    private String id;

    // ==================== 分页参数 ====================
    
    @Builder.Default
    @Schema(description = "页码", example = "1", minimum = "1")
    private Integer page = 1;

    @Builder.Default
    @Schema(description = "每页大小", example = "10", minimum = "1", maximum = "100")
    private Integer pageSize = 10;

    // ==================== 业务方法 ====================

    /**
     * 是否为黑名单操作
     */
    public boolean isBlacklistOperation() {
        return "blacklist".equals(listType);
    }

    /**
     * 是否为白名单操作
     */
    public boolean isWhitelistOperation() {
        return "whitelist".equals(listType);
    }

    /**
     * 验证添加操作的参数
     */
    public void validateForAdd() {
        if (userId == null || userId.trim().isEmpty()) {
            throw new IllegalArgumentException("用户ID不能为空");
        }
        if (source == null || source.trim().isEmpty()) {
            throw new IllegalArgumentException("来源不能为空");
        }
    }

    /**
     * 验证删除操作的参数
     */
    public void validateForDelete() {
        if (id == null || id.trim().isEmpty()) {
            throw new IllegalArgumentException("记录ID不能为空");
        }
    }

    /**
     * 验证查询操作的参数
     */
    public void validateForQuery() {
        if (page != null && page < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        if (pageSize != null && (pageSize < 1 || pageSize > 100)) {
            throw new IllegalArgumentException("每页大小必须在1-100之间");
        }
    }

    /**
     * 转换为黑名单DTO
     */
    public com.iptv.flux.common.dto.BlacklistUserDTO toBlacklistUserDTO() {
        if (!isBlacklistOperation()) {
            throw new IllegalStateException("当前不是黑名单操作");
        }
        return com.iptv.flux.common.dto.BlacklistUserDTO.builder()
                .userId(userId)
                .source(source)
                .remark(remark)
                .build();
    }

    /**
     * 转换为白名单DTO
     */
    public com.iptv.flux.common.dto.WhitelistUserDTO toWhitelistUserDTO() {
        if (!isWhitelistOperation()) {
            throw new IllegalStateException("当前不是白名单操作");
        }
        return com.iptv.flux.common.dto.WhitelistUserDTO.builder()
                .userId(userId)
                .source(source)
                .remark(remark)
                .build();
    }

    /**
     * 转换为列表查询请求DTO
     */
    public com.iptv.flux.common.dto.ListQueryRequestDTO toListQueryRequestDTO() {
        return com.iptv.flux.common.dto.ListQueryRequestDTO.builder()
                .userId(userId)
                .source(source)
                .page(page)
                .pageSize(pageSize)
                .build();
    }

    /**
     * 创建黑名单查询实例
     */
    public static UnifiedListManagementDTO forBlacklistQuery(String userId, String source, Integer page, Integer pageSize) {
        return UnifiedListManagementDTO.builder()
                .listType("blacklist")
                .userId(userId)
                .source(source)
                .page(page)
                .pageSize(pageSize)
                .build();
    }

    /**
     * 创建白名单查询实例
     */
    public static UnifiedListManagementDTO forWhitelistQuery(String userId, String source, Integer page, Integer pageSize) {
        return UnifiedListManagementDTO.builder()
                .listType("whitelist")
                .userId(userId)
                .source(source)
                .page(page)
                .pageSize(pageSize)
                .build();
    }

    /**
     * 创建黑名单添加实例
     */
    public static UnifiedListManagementDTO forBlacklistAdd(String userId, String source, String remark) {
        return UnifiedListManagementDTO.builder()
                .listType("blacklist")
                .userId(userId)
                .source(source)
                .remark(remark)
                .build();
    }

    /**
     * 创建白名单添加实例
     */
    public static UnifiedListManagementDTO forWhitelistAdd(String userId, String source, String remark) {
        return UnifiedListManagementDTO.builder()
                .listType("whitelist")
                .userId(userId)
                .source(source)
                .remark(remark)
                .build();
    }

    /**
     * 创建删除实例
     */
    public static UnifiedListManagementDTO forDelete(String listType, String id) {
        return UnifiedListManagementDTO.builder()
                .listType(listType)
                .id(id)
                .build();
    }
}
