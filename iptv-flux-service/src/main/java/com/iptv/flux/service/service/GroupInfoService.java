package com.iptv.flux.service.service;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.GroupInfoDTO;
import com.iptv.flux.common.dto.GroupQueryRequestDTO;
import com.iptv.flux.common.utils.CacheKeyBuilder;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.model.entity.GroupInfo;
import com.iptv.flux.service.repository.GroupInfoRepository;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GroupInfoService {

    private final GroupInfoRepository groupInfoRepository;
    private final MetricsRegistryUtil metricsRegistry;

    @Qualifier("stringRedisTemplate")
    private final StringRedisTemplate redisTemplate;

    /**
     * 按组ID获取组信息
     *
     * @param groupId Group ID
     * @return 组信息DTO
     */
    @Timed(value = "service.groupinfo.getinfo", percentiles = {0.5, 0.95, 0.99})
    public GroupInfoDTO getGroupInfo(String groupId) {
        long startTime = System.currentTimeMillis();

        // 尝试先从Redis获取
        String redisKey = CacheKeyBuilder.buildGroupInfoRedisKey(groupId);
        HashOperations<String, String, String> hashOps = redisTemplate.opsForHash();

        Map<String, String> groupData = new HashMap<>();
        try {
            groupData = hashOps.entries(redisKey);
            if (!groupData.isEmpty()) {
                metricsRegistry.incrementCounter(UserGroupConstants.GROUP_INFO_CACHE_HIT_METRIC);
                log.debug("Redis缓存命中，分组: {}", groupId);
            }
        } catch (Exception e) {
            log.error("无法从Redis获取groupId的组信息: {}", groupId, e);
            metricsRegistry.incrementCounter(UserGroupConstants.GROUP_INFO_CACHE_ERROR_METRIC);
        }

        if (!groupData.isEmpty()) {
            log.debug("从Redis中检索到的groupId组信息: {}, took {}ms",
                    groupId, System.currentTimeMillis() - startTime);

            return GroupInfoDTO.builder()
                    .groupId(groupId)
                    .businessId(groupData.get("businessId"))
                    .groupName(groupData.get("groupName"))
                    .description(groupData.get("description"))
                    .build();
        }

        // 回退到数据库
        metricsRegistry.incrementCounter(UserGroupConstants.GROUP_INFO_CACHE_MISS_METRIC);
        GroupInfo groupInfo = groupInfoRepository.findByGroupId(groupId);
        if (groupInfo == null) {
            log.debug("在数据库中找不到groupId的组: {}", groupId);
            return null;
        }

        // 更新Redis缓存
        updateGroupInfoCache(groupInfo);

        log.debug("从数据库中获取到groupId的群组信息: {}, took {}ms",
                groupId, System.currentTimeMillis() - startTime);

        return GroupInfoDTO.builder()
                .groupId(groupInfo.getGroupId())
                .businessId(groupInfo.getBusinessId())
                .groupName(groupInfo.getGroupName())
                .description(groupInfo.getDescription())
                .build();
    }

    /**
     * 通过组ID批量获取组信息
     *
     * @param groupIds List of group IDs
     * @return 群组ID到群组信息的映射
     */
    @Timed(value = "service.groupinfo.batch", percentiles = {0.5, 0.95, 0.99})
    public Map<String, GroupInfoDTO> getGroupInfoBatch(List<String> groupIds) {
        long startTime = System.currentTimeMillis();
        log.debug("获取 {} 组群的信息", groupIds.size());

        // 直接查询数据库以提高批处理效率
        Map<String, GroupInfoDTO> result = groupInfoRepository.findGroupsByIds(groupIds);

        // 异步更新缓存
        for (GroupInfoDTO dto : result.values()) {
            try {
                GroupInfo groupInfo = GroupInfo.builder()
                        .groupId(dto.getGroupId())
                        .businessId(dto.getBusinessId())
                        .groupName(dto.getGroupName())
                        .description(dto.getDescription())
                        .build();

                updateGroupInfoCache(groupInfo);
            } catch (Exception e) {
                log.warn("更新groupId缓存失败: {}", dto.getGroupId(), e);
            }
        }

        log.debug("批处理组信息检索了 {} 个组，共请求了 {} 个组，耗时 {}ms",
                result.size(), groupIds.size(), System.currentTimeMillis() - startTime);

        return result;
    }

    /**
     * 保存组信息
     *
     * @param groupInfoDTO 群信息保存
     */
    @Timed(value = "service.groupinfo.save", percentiles = {0.5, 0.95, 0.99})
    public void saveGroupInfo(GroupInfoDTO groupInfoDTO) {
        long startTime = System.currentTimeMillis();
        log.info("保存群组信息 for groupId: {}", groupInfoDTO.getGroupId());

        // 保存到数据库
        GroupInfo groupInfo = GroupInfo.builder()
                .groupId(groupInfoDTO.getGroupId())
                .businessId(groupInfoDTO.getBusinessId())
                .groupName(groupInfoDTO.getGroupName())
                .description(groupInfoDTO.getDescription())
                .build();

        groupInfoRepository.saveGroupInfo(groupInfo);

        // 更新 Redis 缓存
        updateGroupInfoCache(groupInfo);

        log.debug("Group info saved for groupId: {}, took {}ms",
                groupInfoDTO.getGroupId(), System.currentTimeMillis() - startTime);
    }

    private void updateGroupInfoCache(GroupInfo groupInfo) {
        String redisKey = CacheKeyBuilder.buildGroupInfoRedisKey(groupInfo.getGroupId());
        Map<String, String> hashEntries = new HashMap<>();
        hashEntries.put("businessId", groupInfo.getBusinessId());
        hashEntries.put("groupName", groupInfo.getGroupName());
        hashEntries.put("description", groupInfo.getDescription() != null ? groupInfo.getDescription() : "");

        try {
            redisTemplate.opsForHash().putAll(redisKey, hashEntries);
            log.debug("更新了groupId的Redis缓存: {}", groupInfo.getGroupId());
        } catch (Exception e) {
            log.error("未能更新groupId的组信息缓存: {}", groupInfo.getGroupId(), e);
            metricsRegistry.incrementCounter(UserGroupConstants.GROUP_INFO_CACHE_UPDATE_ERROR_METRIC);
        }
    }

    /**
     * 获取所有分组信息
     *
     * @return 分组信息列表
     */
    @Timed(value = "service.groupinfo.getAll", percentiles = {0.5, 0.95, 0.99})
    public List<GroupInfoDTO> getAllGroups() {
        long startTime = System.currentTimeMillis();

        log.debug("流量平台-----> 获取所有分组信息");

        List<GroupInfoDTO> groups = groupInfoRepository.findAllGroups();

        log.debug("流量平台-----> 获取到 {} 个分组，耗时 {}ms",
                groups.size(), System.currentTimeMillis() - startTime);

        return groups;
    }

    /**
     * 根据来源获取分组信息
     *
     * @param source 来源/运营商
     * @return 分组信息列表
     */
    @Timed(value = "service.groupinfo.getBySource", percentiles = {0.5, 0.95, 0.99})
    public List<GroupInfoDTO> getGroupsBySource(String source) {
        long startTime = System.currentTimeMillis();

        log.debug("流量平台-----> 获取来源为 {} 的分组信息", source);

        List<GroupInfoDTO> groups = groupInfoRepository.getGroupsBySource(source);

        log.debug("流量平台-----> 获取到来源 {} 的 {} 个分组，耗时 {}ms",
                source, groups.size(), System.currentTimeMillis() - startTime);

        return groups;
    }

    /**
     * 自动创建或更新分组信息
     *
     * @param groupId 分组ID
     * @param businessId 业务ID（可选）
     * @param groupName 分组名称（可选）
     * @param description 分组描述（可选）
     * @param source 来源标识
     * @return 创建或更新的分组信息
     */
    @Timed(value = "service.groupinfo.autoCreate", percentiles = {0.5, 0.95, 0.99})
    public GroupInfoDTO autoCreateOrUpdateGroup(String groupId, String businessId,
                                               String groupName, String description, String source) {
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 开始自动创建/更新分组，分组ID: {}, 业务ID: {}, 来源: {}",
                groupId, businessId, source);

        try {
            // 检查分组是否已存在
            GroupInfoDTO existingGroup = getGroupInfo(groupId);

            if (existingGroup != null) {
                log.debug("流量平台-----> 分组已存在，分组ID: {}，将更新信息", groupId);
                return updateExistingGroup(existingGroup, businessId, groupName, description);
            } else {
                log.debug("流量平台-----> 分组不存在，将创建新分组，分组ID: {}", groupId);
                return createNewGroup(groupId, businessId, groupName, description, source);
            }

        } catch (Exception e) {
            log.error("流量平台-----> 自动创建/更新分组失败，分组ID: {}", groupId, e);
            metricsRegistry.incrementCounter(UserGroupConstants.GROUP_AUTO_CREATE_ERROR_METRIC);
            throw e;
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            log.debug("流量平台-----> 分组自动创建/更新完成，分组ID: {}，耗时: {}ms", groupId, duration);
        }
    }

    /**
     * 更新已存在的分组信息
     */
    private GroupInfoDTO updateExistingGroup(GroupInfoDTO existingGroup, String businessId,
                                           String groupName, String description) {
        // 构建更新的分组信息，保留原有信息作为默认值
        GroupInfoDTO updatedGroup = GroupInfoDTO.builder()
                .groupId(existingGroup.getGroupId())
                .businessId(businessId != null ? businessId : existingGroup.getBusinessId())
                .groupName(groupName != null ? groupName : existingGroup.getGroupName())
                .description(description != null ? description : existingGroup.getDescription())
                .build();

        // 检查是否有实际更新
        boolean needUpdate = false;
        if (businessId != null && !businessId.equals(existingGroup.getBusinessId())) {
            needUpdate = true;
        }
        if (groupName != null && !groupName.equals(existingGroup.getGroupName())) {
            needUpdate = true;
        }
        if (description != null && !description.equals(existingGroup.getDescription())) {
            needUpdate = true;
        }

        if (needUpdate) {
            log.info("流量平台-----> 更新分组信息，分组ID: {}", existingGroup.getGroupId());
            saveGroupInfo(updatedGroup);
            metricsRegistry.incrementCounter(UserGroupConstants.GROUP_AUTO_UPDATE_SUCCESS_METRIC);
        } else {
            log.debug("流量平台-----> 分组信息无需更新，分组ID: {}", existingGroup.getGroupId());
            metricsRegistry.incrementCounter(UserGroupConstants.GROUP_AUTO_UPDATE_SKIP_METRIC);
        }

        return updatedGroup;
    }

    /**
     * 创建新的分组信息
     */
    private GroupInfoDTO createNewGroup(String groupId, String businessId,
                                      String groupName, String description, String source) {
        // 生成默认值
        String finalBusinessId = businessId != null ? businessId : generateDefaultBusinessId(groupId, source);
        String finalGroupName = groupName != null ? groupName : generateDefaultGroupName(groupId);
        String finalDescription = description != null ? description : generateDefaultDescription(groupId, source);

        GroupInfoDTO newGroup = GroupInfoDTO.builder()
                .groupId(groupId)
                .businessId(finalBusinessId)
                .groupName(finalGroupName)
                .description(finalDescription)
                .build();

        log.info("流量平台-----> 创建新分组，分组ID: {}, 业务ID: {}, 分组名称: {}",
                groupId, finalBusinessId, finalGroupName);

        saveGroupInfo(newGroup);
        metricsRegistry.incrementCounter(UserGroupConstants.GROUP_AUTO_CREATE_SUCCESS_METRIC);

        return newGroup;
    }

    /**
     * 生成默认的业务ID
     */
    private String generateDefaultBusinessId(String groupId, String source) {
        return String.format("%s_%s", source.toUpperCase(), groupId);
    }

    /**
     * 生成默认的分组名称
     */
    private String generateDefaultGroupName(String groupId) {
        // 尝试从groupId中提取有意义的名称
        String name = groupId;

        // 如果包含下划线，取最后一部分作为名称
        if (name.contains("_")) {
            String[] parts = name.split("_");
            name = parts[parts.length - 1];
        }

        // 如果包含连字符，取最后一部分作为名称
        if (name.contains("-")) {
            String[] parts = name.split("-");
            name = parts[parts.length - 1];
        }

        // 首字母大写
        if (name.length() > 0) {
            name = name.substring(0, 1).toUpperCase() + name.substring(1);
        }

        return String.format("分组-%s", name);
    }

    /**
     * 生成默认的分组描述
     */
    private String generateDefaultDescription(String groupId, String source) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        return String.format("自动创建的分组 (来源: %s, 创建时间: %s)", source, timestamp);
    }

    /**
     * 批量自动创建分组信息
     *
     * @param groupIds 分组ID列表
     * @param businessId 统一的业务ID（可选）
     * @param source 来源标识
     * @return 创建成功的分组数量
     */
    @Timed(value = "service.groupinfo.batchAutoCreate", percentiles = {0.5, 0.95, 0.99})
    public int batchAutoCreateGroups(List<String> groupIds, String businessId, String source) {
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 开始批量自动创建分组，数量: {}, 来源: {}", groupIds.size(), source);

        int successCount = 0;
        int errorCount = 0;

        for (String groupId : groupIds) {
            try {
                autoCreateOrUpdateGroup(groupId, businessId, null, null, source);
                successCount++;
            } catch (Exception e) {
                log.error("流量平台-----> 批量创建分组失败，分组ID: {}", groupId, e);
                errorCount++;
            }
        }

        long duration = System.currentTimeMillis() - startTime;
        log.info("流量平台-----> 批量分组创建完成，总数: {}, 成功: {}, 错误: {}, 耗时: {}ms",
                groupIds.size(), successCount, errorCount, duration);

        metricsRegistry.incrementCounter(UserGroupConstants.GROUP_BATCH_CREATE_TOTAL_METRIC, (double) groupIds.size());
        metricsRegistry.incrementCounter(UserGroupConstants.GROUP_BATCH_CREATE_SUCCESS_METRIC, (double) successCount);
        metricsRegistry.incrementCounter(UserGroupConstants.GROUP_BATCH_CREATE_ERROR_METRIC, (double) errorCount);

        return successCount;
    }

    /**
     * 根据条件查询分组信息
     *
     * @param request 查询条件
     * @return 分组信息列表
     */
    @Timed(value = "service.groupinfo.queryGroups", percentiles = {0.5, 0.95, 0.99})
    public List<GroupInfoDTO> queryGroups(GroupQueryRequestDTO request) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始条件查询分组，分组名称: {}, 业务ID: {}, 来源: {}",
                request.getTrimmedGroupName(), request.getTrimmedBusinessId(), request.getTrimmedSource());

        try {
            List<GroupInfoDTO> result;

            // 如果所有查询条件都为空，返回所有分组
            if (request.isEmpty()) {
                log.debug("流量平台-----> 查询条件为空，返回所有分组");
                result = getAllGroups();
            } else {
                // 根据条件查询
                result = groupInfoRepository.queryGroupsByConditions(request);
            }

            long duration = System.currentTimeMillis() - startTime;
            log.debug("流量平台-----> 条件查询分组完成，结果数量: {}, 耗时: {}ms", result.size(), duration);

            // 记录指标
            metricsRegistry.incrementCounter("service.groupinfo.queryGroups.success");
            metricsRegistry.recordExecutionTime("service.groupinfo.queryGroups.duration", startTime);

            return result;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("流量平台-----> 条件查询分组失败，耗时: {}ms", duration, e);

            metricsRegistry.incrementCounter("service.groupinfo.queryGroups.error");
            throw new RuntimeException("查询分组信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存或更新分组信息（用于FTP文件处理）
     *
     * @param groupId 分组ID
     * @param groupName 分组名称
     * @param platform 平台标识
     * @param source 来源标识（运营商代码）
     * @param description 描述
     */
    @Timed(value = "service.groupinfo.saveOrUpdate", percentiles = {0.5, 0.95, 0.99})
    public void saveOrUpdateGroupInfo(String groupId, String groupName, String platform,
                                    String source, String description) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 保存或更新分组信息: groupId={}, platform={}, source={}",
                groupId, platform, source);

        try {
            // 使用现有的自动创建功能，但需要扩展支持新字段
            GroupInfoDTO groupInfo = autoCreateOrUpdateGroup(groupId, null, groupName, description, platform);

            // 如果需要更新平台信息，调用Repository的扩展方法
            groupInfoRepository.updateGroupExtendedInfo(groupId, platform);

            log.debug("流量平台-----> 分组信息保存完成: groupId={}, 耗时: {}ms",
                    groupId, System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("流量平台-----> 保存分组信息失败: groupId={}", groupId, e);
            throw new RuntimeException("保存分组信息失败: " + e.getMessage(), e);
        }
    }

}