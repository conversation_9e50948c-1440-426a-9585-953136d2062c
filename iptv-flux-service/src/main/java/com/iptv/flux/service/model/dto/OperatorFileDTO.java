package com.iptv.flux.service.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 运营商文件DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "运营商文件信息")
public class OperatorFileDTO {

    @NotBlank(message = "来源标识不能为空")
    @Pattern(regexp = "^(dx|lt|yd)$", message = "来源标识必须是dx、lt或yd")
    @Schema(description = "来源标识（运营商代码）", example = "dx", allowableValues = {"dx", "lt", "yd"})
    private String source;

    @NotBlank(message = "文件URL不能为空")
    @Schema(description = "文件URL", example = "ftp://***********/data/dx_users.txt")
    private String fileUrl;

    @Schema(description = "文件类型", example = "用户分组文件")
    private String fileType;

    @Schema(description = "文件大小（字节）")
    private Long fileSize;

    @Schema(description = "MD5校验值")
    private String md5sum;
}
