package com.iptv.flux.service.service;

import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.model.dto.OperatorFileDTO;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * FTP文件处理服务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FtpFileProcessorService {

    private final MetricsRegistryUtil metricsRegistry;
    private final GroupInfoService groupInfoService;
    private final UserGroupService userGroupService;

    @Value("${ftp.download.temp-dir:/tmp/ftp-downloads}")
    private String tempDownloadDir;

    @Value("${ftp.download.timeout:30000}")
    private int downloadTimeout;

    /**
     * 处理groups文件
     */
    @Timed(value = "service.ftp.process.groups", percentiles = {0.5, 0.95, 0.99})
    public void processGroupsFile(String taskId, String fileUrl) {
        log.info("流量平台-----> 开始处理groups文件，任务ID: {}, URL: {}", taskId, fileUrl);
        
        try {
            // 1. 下载文件
            Path localFile = downloadFile(fileUrl, "groups_" + taskId + ".txt");
            
            // 2. 验证文件
            validateFile(localFile);
            
            // 3. 解析文件
            List<String> lines = Files.readAllLines(localFile);
            
            // 4. 处理数据
            processGroupsData(taskId, lines);
            
            // 5. 清理临时文件
            Files.deleteIfExists(localFile);
            
            log.info("流量平台-----> groups文件处理完成，任务ID: {}", taskId);
            
        } catch (Exception e) {
            log.error("流量平台-----> 处理groups文件失败，任务ID: {}, URL: {}", taskId, fileUrl, e);
            throw new RuntimeException("处理groups文件失败", e);
        }
    }

    /**
     * 处理strategies文件
     */
    @Timed(value = "service.ftp.process.strategies", percentiles = {0.5, 0.95, 0.99})
    public void processStrategiesFile(String taskId, String fileUrl) {
        log.info("流量平台-----> 开始处理strategies文件，任务ID: {}, URL: {}", taskId, fileUrl);
        
        try {
            // 1. 下载文件
            Path localFile = downloadFile(fileUrl, "strategies_" + taskId + ".txt");
            
            // 2. 验证文件
            validateFile(localFile);
            
            // 3. 解析文件
            List<String> lines = Files.readAllLines(localFile);
            
            // 4. 处理数据
            processStrategiesData(taskId, lines);
            
            // 5. 清理临时文件
            Files.deleteIfExists(localFile);
            
            log.info("流量平台-----> strategies文件处理完成，任务ID: {}", taskId);
            
        } catch (Exception e) {
            log.error("流量平台-----> 处理strategies文件失败，任务ID: {}, URL: {}", taskId, fileUrl, e);
            throw new RuntimeException("处理strategies文件失败", e);
        }
    }

    /**
     * 处理userGroup文件
     */
    @Timed(value = "service.ftp.process.usergroup", percentiles = {0.5, 0.95, 0.99})
    public void processUserGroupFile(String taskId, String fileUrl) {
        log.info("流量平台-----> 开始处理userGroup文件，任务ID: {}, URL: {}", taskId, fileUrl);
        
        try {
            // 1. 下载文件
            Path localFile = downloadFile(fileUrl, "userGroup_" + taskId + ".txt");
            
            // 2. 验证文件
            validateFile(localFile);
            
            // 3. 解析文件
            List<String> lines = Files.readAllLines(localFile);
            
            // 4. 处理数据
            processUserGroupData(taskId, lines);
            
            // 5. 清理临时文件
            Files.deleteIfExists(localFile);
            
            log.info("流量平台-----> userGroup文件处理完成，任务ID: {}", taskId);
            
        } catch (Exception e) {
            log.error("流量平台-----> 处理userGroup文件失败，任务ID: {}, URL: {}", taskId, fileUrl, e);
            throw new RuntimeException("处理userGroup文件失败", e);
        }
    }

    /**
     * 处理运营商文件
     */
    @Timed(value = "service.ftp.process.operator", percentiles = {0.5, 0.95, 0.99})
    public void processOperatorFile(String taskId, OperatorFileDTO operatorFile) {
        log.info("流量平台-----> 开始处理运营商文件，任务ID: {}, 来源: {}, URL: {}",
                taskId, operatorFile.getSource(), operatorFile.getFileUrl());
        
        try {
            // 1. 下载文件
            Path localFile = downloadFile(operatorFile.getFileUrl(),
                    operatorFile.getSource() + "_" + taskId + ".txt");
            
            // 2. 验证文件
            validateFile(localFile);
            
            // 3. 验证MD5（如果提供）
            if (operatorFile.getMd5sum() != null) {
                String actualMd5 = calculateMd5(localFile);
                if (!actualMd5.equalsIgnoreCase(operatorFile.getMd5sum())) {
                    throw new RuntimeException("MD5校验失败，期望: " + operatorFile.getMd5sum() + 
                            ", 实际: " + actualMd5);
                }
            }
            
            // 4. 解析文件
            List<String> lines = Files.readAllLines(localFile);
            
            // 5. 处理数据
            processOperatorData(taskId, operatorFile.getSource(), lines);

            // 6. 清理临时文件
            Files.deleteIfExists(localFile);

            log.info("流量平台-----> 运营商文件处理完成，任务ID: {}, 来源: {}",
                    taskId, operatorFile.getSource());

        } catch (Exception e) {
            log.error("流量平台-----> 处理运营商文件失败，任务ID: {}, 来源: {}, URL: {}",
                    taskId, operatorFile.getSource(), operatorFile.getFileUrl(), e);
            throw new RuntimeException("处理运营商文件失败", e);
        }
    }

    /**
     * 下载文件
     */
    private Path downloadFile(String fileUrl, String fileName) throws IOException {
        log.debug("流量平台-----> 开始下载文件: {} -> {}", fileUrl, fileName);
        
        // 创建临时目录
        Path tempDir = Paths.get(tempDownloadDir);
        Files.createDirectories(tempDir);
        
        Path localFile = tempDir.resolve(fileName);
        
        // 下载文件
        try (InputStream in = new URL(fileUrl).openStream()) {
            Files.copy(in, localFile);
        }
        
        log.debug("流量平台-----> 文件下载完成: {}, 大小: {} bytes", fileName, Files.size(localFile));
        return localFile;
    }

    /**
     * 验证文件
     */
    private void validateFile(Path file) throws IOException {
        if (!Files.exists(file)) {
            throw new IOException("文件不存在: " + file);
        }
        
        if (Files.size(file) == 0) {
            throw new IOException("文件为空: " + file);
        }
        
        log.debug("流量平台-----> 文件验证通过: {}", file.getFileName());
    }

    /**
     * 计算MD5
     */
    private String calculateMd5(Path file) throws Exception {
        MessageDigest md = MessageDigest.getInstance("MD5");
        try (InputStream is = Files.newInputStream(file)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = is.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }
        }
        
        byte[] digest = md.digest();
        StringBuilder sb = new StringBuilder();
        for (byte b : digest) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    /**
     * 处理groups数据
     */
    private void processGroupsData(String taskId, List<String> lines) {
        log.info("流量平台-----> 开始处理groups数据，任务ID: {}, 行数: {}", taskId, lines.size());

        int processedCount = 0;
        int errorCount = 0;

        for (String line : lines) {
            if (line.trim().isEmpty() || line.startsWith("#")) {
                continue; // 跳过空行和注释行
            }

            try {
                String[] parts = line.split("\\|");
                if (parts.length >= 4) {
                    String groupId = parts[0].trim();
                    String groupName = parts[1].trim();
                    String platform = parts[2].trim();
                    String source = parts[3].trim(); // 来源标识（运营商代码）
                    String description = parts.length > 4 ? parts[4].trim() : "";

                    // 调用groupInfoService保存或更新数据
                    groupInfoService.saveOrUpdateGroupInfo(groupId, groupName, platform, source, description);

                    processedCount++;
                    log.debug("流量平台-----> 处理groups记录: {}", groupId);
                } else {
                    log.warn("流量平台-----> groups行格式不正确: {}", line);
                    errorCount++;
                }
            } catch (Exception e) {
                log.warn("流量平台-----> 处理groups行失败: {}", line, e);
                errorCount++;
            }
        }

        log.info("流量平台-----> groups数据处理完成，任务ID: {}, 成功: {}, 失败: {}",
                taskId, processedCount, errorCount);
    }

    /**
     * 处理strategies数据
     */
    private void processStrategiesData(String taskId, List<String> lines) {
        log.info("流量平台-----> 开始处理strategies数据，任务ID: {}, 行数: {}", taskId, lines.size());
        
        // TODO: 实现具体的strategies数据处理逻辑
        // 解析格式：strategyId|strategyName|startTime|endTime|action|createUser|createTime|source|description|status
        
        for (String line : lines) {
            if (line.trim().isEmpty() || line.startsWith("#")) {
                continue;
            }
            
            try {
                String[] parts = line.split("\\|");
                if (parts.length >= 6) {
                    String strategyId = parts[0].trim();
                    String strategyName = parts[1].trim();
                    // ... 其他字段解析
                    
                    log.debug("流量平台-----> 处理strategies记录: {}", strategyId);
                }
            } catch (Exception e) {
                log.warn("流量平台-----> 处理strategies行失败: {}", line, e);
            }
        }
        
        log.info("流量平台-----> strategies数据处理完成，任务ID: {}", taskId);
    }

    /**
     * 处理userGroup数据
     */
    private void processUserGroupData(String taskId, List<String> lines) {
        log.info("流量平台-----> 开始处理userGroup数据，任务ID: {}, 行数: {}", taskId, lines.size());
        
        // TODO: 实现具体的userGroup数据处理逻辑
        // 解析格式：userId|groupIds
        
        for (String line : lines) {
            if (line.trim().isEmpty() || line.startsWith("#")) {
                continue;
            }
            
            try {
                String[] parts = line.split("\\|");
                if (parts.length >= 2) {
                    String userId = parts[0].trim();
                    String groupIds = parts[1].trim();
                    
                    // 调用userGroupService保存数据
                    // userGroupService.saveUserGroupRelation(userId, groupIds);
                    
                    log.debug("流量平台-----> 处理userGroup记录: {}", userId);
                }
            } catch (Exception e) {
                log.warn("流量平台-----> 处理userGroup行失败: {}", line, e);
            }
        }
        
        log.info("流量平台-----> userGroup数据处理完成，任务ID: {}", taskId);
    }

    /**
     * 处理运营商数据
     */
    private void processOperatorData(String taskId, String source, List<String> lines) {
        log.info("流量平台-----> 开始处理运营商数据，任务ID: {}, 来源: {}, 行数: {}",
                taskId, source, lines.size());
        
        // TODO: 实现具体的运营商数据处理逻辑
        
        for (String line : lines) {
            if (line.trim().isEmpty() || line.startsWith("#")) {
                continue;
            }
            
            try {
                // 根据具体格式解析数据
                log.debug("流量平台-----> 处理运营商记录: {}", line);
            } catch (Exception e) {
                log.warn("流量平台-----> 处理运营商行失败: {}", line, e);
            }
        }
        
        log.info("流量平台-----> 运营商数据处理完成，任务ID: {}, 来源: {}", taskId, source);
    }
}
