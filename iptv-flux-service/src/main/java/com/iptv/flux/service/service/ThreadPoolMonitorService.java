package com.iptv.flux.service.service;

import com.iptv.flux.common.utils.MetricsRegistryUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池监控服务
 * 负责监控线程池状态，记录指标，预警异常情况
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class ThreadPoolMonitorService {

    private final MetricsRegistryUtil metricsRegistry;

    @Qualifier("dataProcessExecutor")
    private final Executor dataProcessExecutor;

    @Qualifier("fileDownloadExecutor")
    private final Executor fileDownloadExecutor;

    @Qualifier("dataImportExecutor")
    private final Executor dataImportExecutor;

    /**
     * 定期监控线程池状态
     */
    @Scheduled(fixedDelay = 30000) // 每30秒监控一次
    public void monitorThreadPools() {
        try {
            monitorThreadPool("dataProcessExecutor", dataProcessExecutor);
            monitorThreadPool("fileDownloadExecutor", fileDownloadExecutor);
            monitorThreadPool("dataImportExecutor", dataImportExecutor);
        } catch (Exception e) {
            log.error("流量平台-----> 线程池监控异常", e);
        }
    }

    /**
     * 监控单个线程池
     */
    private void monitorThreadPool(String poolName, Executor executor) {
        if (!(executor instanceof ThreadPoolTaskExecutor)) {
            return;
        }

        ThreadPoolTaskExecutor taskExecutor = (ThreadPoolTaskExecutor) executor;
        ThreadPoolExecutor threadPoolExecutor = taskExecutor.getThreadPoolExecutor();

        if (threadPoolExecutor == null) {
            return;
        }

        // 获取线程池状态
        int corePoolSize = threadPoolExecutor.getCorePoolSize();
        int maximumPoolSize = threadPoolExecutor.getMaximumPoolSize();
        int activeCount = threadPoolExecutor.getActiveCount();
        long completedTaskCount = threadPoolExecutor.getCompletedTaskCount();
        int queueSize = threadPoolExecutor.getQueue().size();
        int poolSize = threadPoolExecutor.getPoolSize();
        long taskCount = threadPoolExecutor.getTaskCount();

        // 计算使用率
        double activeRatio = (double) activeCount / maximumPoolSize;
        double queueRatio = (double) queueSize / (queueSize + 1000);
        
        // 记录指标
        metricsRegistry.recordGauge("thread.pool.core.size", corePoolSize, "pool", poolName);
        metricsRegistry.recordGauge("thread.pool.max.size", maximumPoolSize, "pool", poolName);
        metricsRegistry.recordGauge("thread.pool.active.count", activeCount, "pool", poolName);
        metricsRegistry.recordGauge("thread.pool.pool.size", poolSize, "pool", poolName);
        metricsRegistry.recordGauge("thread.pool.queue.size", queueSize, "pool", poolName);
        metricsRegistry.recordGauge("thread.pool.completed.tasks", completedTaskCount, "pool", poolName);
        metricsRegistry.recordGauge("thread.pool.total.tasks", taskCount, "pool", poolName);
        metricsRegistry.recordGauge("thread.pool.active.ratio", activeRatio, "pool", poolName);
        metricsRegistry.recordGauge("thread.pool.queue.ratio", queueRatio, "pool", poolName);

        // 记录日志
        log.debug("流量平台-----> 线程池状态监控 [{}]: 核心={}, 最大={}, 活跃={}, 池大小={}, 队列={}, 已完成={}, 总任务={}, 活跃率={:.2f}%, 队列率={:.2f}%",
                poolName, corePoolSize, maximumPoolSize, activeCount, poolSize, queueSize, 
                completedTaskCount, taskCount, activeRatio * 100, queueRatio * 100);

        // 预警检查
        checkThreadPoolHealth(poolName, activeRatio, queueRatio, activeCount, queueSize);
    }

    /**
     * 检查线程池健康状态并预警
     */
    private void checkThreadPoolHealth(String poolName, double activeRatio, double queueRatio, 
                                     int activeCount, int queueSize) {
        
        // 活跃线程比例过高预警
        if (activeRatio > 0.9) {
            log.warn("流量平台-----> 线程池活跃率过高预警 [{}]: 活跃率={:.2f}%, 活跃线程数={}", 
                    poolName, activeRatio * 100, activeCount);
            metricsRegistry.incrementCounter("thread.pool.warning.high.active", "pool", poolName);
        }

        // 队列积压预警
        if (queueSize > 500) {
            log.warn("流量平台-----> 线程池队列积压预警 [{}]: 队列大小={}", poolName, queueSize);
            metricsRegistry.incrementCounter("thread.pool.warning.queue.backlog", "pool", poolName);
        }

        // 队列比例过高预警
        if (queueRatio > 0.8) {
            log.warn("流量平台-----> 线程池队列使用率过高预警 [{}]: 队列率={:.2f}%", 
                    poolName, queueRatio * 100);
            metricsRegistry.incrementCounter("thread.pool.warning.high.queue", "pool", poolName);
        }
    }

    /**
     * 获取线程池状态摘要
     */
    public String getThreadPoolSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("线程池状态摘要:\n");
        
        appendThreadPoolStatus(summary, "dataProcessExecutor", dataProcessExecutor);
        appendThreadPoolStatus(summary, "fileDownloadExecutor", fileDownloadExecutor);
        appendThreadPoolStatus(summary, "dataImportExecutor", dataImportExecutor);
        
        return summary.toString();
    }

    /**
     * 添加线程池状态到摘要
     */
    private void appendThreadPoolStatus(StringBuilder summary, String poolName, Executor executor) {
        if (!(executor instanceof ThreadPoolTaskExecutor)) {
            summary.append(String.format("  %s: 不是ThreadPoolTaskExecutor类型\n", poolName));
            return;
        }

        ThreadPoolTaskExecutor taskExecutor = (ThreadPoolTaskExecutor) executor;
        ThreadPoolExecutor threadPoolExecutor = taskExecutor.getThreadPoolExecutor();

        if (threadPoolExecutor == null) {
            summary.append(String.format("  %s: ThreadPoolExecutor为null\n", poolName));
            return;
        }

        int activeCount = threadPoolExecutor.getActiveCount();
        int poolSize = threadPoolExecutor.getPoolSize();
        int queueSize = threadPoolExecutor.getQueue().size();
        long completedTaskCount = threadPoolExecutor.getCompletedTaskCount();

        summary.append(String.format("  %s: 活跃=%d, 池大小=%d, 队列=%d, 已完成=%d\n",
                poolName, activeCount, poolSize, queueSize, completedTaskCount));
    }

    /**
     * 强制关闭所有线程池（紧急情况使用）
     */
    public void emergencyShutdown() {
        log.warn("流量平台-----> 执行线程池紧急关闭");
        
        shutdownExecutor("dataProcessExecutor", dataProcessExecutor);
        shutdownExecutor("fileDownloadExecutor", fileDownloadExecutor);
        shutdownExecutor("dataImportExecutor", dataImportExecutor);
        
        metricsRegistry.incrementCounter("thread.pool.emergency.shutdown");
    }

    /**
     * 关闭单个执行器
     */
    private void shutdownExecutor(String poolName, Executor executor) {
        try {
            if (executor instanceof ThreadPoolTaskExecutor) {
                ThreadPoolTaskExecutor taskExecutor = (ThreadPoolTaskExecutor) executor;
                taskExecutor.shutdown();
                log.info("流量平台-----> 线程池已关闭: {}", poolName);
            }
        } catch (Exception e) {
            log.error("流量平台-----> 关闭线程池失败: {}", poolName, e);
        }
    }
}
