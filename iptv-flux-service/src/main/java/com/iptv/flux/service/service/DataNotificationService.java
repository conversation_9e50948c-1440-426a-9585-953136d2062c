package com.iptv.flux.service.service;

import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.model.dto.OperatorFileDTO;
import com.iptv.flux.service.model.dto.UpdateNotificationDTO;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.concurrent.Executor;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 数据通知处理服务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class DataNotificationService {

    private final FtpFileProcessorService ftpFileProcessorService;
    private final MetricsRegistryUtil metricsRegistry;
    private final AsyncTaskManager asyncTaskManager;

    // 任务状态缓存
    private final Map<String, String> taskStatusCache = new ConcurrentHashMap<>();

    /**
     * 处理数据更新通知
     * 
     * @param notification 更新通知
     * @return 任务ID
     */
    @Timed(value = "service.data.notification.process", percentiles = {0.5, 0.95, 0.99})
    public String processUpdateNotification(UpdateNotificationDTO notification) {
        String taskId = generateTaskId();
        
        log.info("流量平台-----> 开始处理数据更新通知，任务ID: {}, 更新时间: {}", 
                taskId, notification.getUpdateTime());

        try {
            // 更新任务状态
            updateTaskStatus(taskId, "PROCESSING");

            // 异步处理文件
            processFilesAsync(taskId, notification);

            // 记录指标
            metricsRegistry.incrementCounter("data.notification.received", 
                    "source", notification.getSource() != null ? notification.getSource() : "unknown");

            return taskId;
        } catch (Exception e) {
            updateTaskStatus(taskId, "FAILED");
            log.error("流量平台-----> 处理数据更新通知失败，任务ID: {}", taskId, e);
            throw new RuntimeException("处理通知失败: " + e.getMessage(), e);
        }
    }

    /**
     * 异步处理文件 - 使用任务管理器进行健壮的并行处理
     */
    @Async("dataProcessExecutor")
    public CompletableFuture<Void> processFilesAsync(String taskId, UpdateNotificationDTO notification) {
        try {
            log.info("流量平台-----> 开始健壮的并行异步处理文件，任务ID: {}", taskId);

            // 1. 构建任务列表
            List<AsyncTaskManager.AsyncTask> tasks = buildAsyncTasks(taskId, notification);

            if (tasks.isEmpty()) {
                log.warn("流量平台-----> 没有需要处理的文件，任务ID: {}", taskId);
                updateTaskStatus(taskId, "COMPLETED");
                return CompletableFuture.completedFuture(null);
            }

            // 2. 使用任务管理器执行所有任务
            AsyncTaskManager.TaskExecutionSummary summary = asyncTaskManager.executeTasksWithManagement(taskId, tasks);

            // 3. 根据执行结果更新任务状态
            updateTaskStatusFromSummary(taskId, summary);

            log.info("流量平台-----> 文件处理完成，任务ID: {}, 总数: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                    taskId, summary.getTotalTasks(), summary.getSuccessTasks(),
                    summary.getFailedTasks(), summary.getTotalExecutionTimeMs());

        } catch (Exception e) {
            updateTaskStatus(taskId, "FAILED");
            log.error("流量平台-----> 健壮异步处理文件失败，任务ID: {}", taskId, e);
            metricsRegistry.incrementCounter("data.notification.processed.failed",
                    "taskId", taskId, "error", e.getClass().getSimpleName());
        }

        return CompletableFuture.completedFuture(null);
    }

    /**
     * 构建异步任务列表
     */
    private List<AsyncTaskManager.AsyncTask> buildAsyncTasks(String taskId, UpdateNotificationDTO notification) {
        List<AsyncTaskManager.AsyncTask> tasks = new ArrayList<>();

        // 1. groups文件处理任务
        if (notification.getGroupsFileUrl() != null) {
            tasks.add(new AsyncTaskManager.AsyncTask() {
                @Override
                public void execute() throws Exception {
                    ftpFileProcessorService.processGroupsFile(taskId, notification.getGroupsFileUrl());
                }

                @Override
                public String getTaskName() {
                    return "processGroupsFile";
                }
            });
        }

        // 2. strategies文件处理任务
        if (notification.getStrategiesFileUrl() != null) {
            tasks.add(new AsyncTaskManager.AsyncTask() {
                @Override
                public void execute() throws Exception {
                    ftpFileProcessorService.processStrategiesFile(taskId, notification.getStrategiesFileUrl());
                }

                @Override
                public String getTaskName() {
                    return "processStrategiesFile";
                }
            });
        }

        // 3. userGroup文件处理任务
        if (notification.getUserGroupFileUrl() != null) {
            tasks.add(new AsyncTaskManager.AsyncTask() {
                @Override
                public void execute() throws Exception {
                    ftpFileProcessorService.processUserGroupFile(taskId, notification.getUserGroupFileUrl());
                }

                @Override
                public String getTaskName() {
                    return "processUserGroupFile";
                }
            });
        }

        // 4. 运营商文件处理任务
        if (notification.getOperatorFiles() != null) {
            for (OperatorFileDTO operatorFile : notification.getOperatorFiles()) {
                tasks.add(new AsyncTaskManager.AsyncTask() {
                    @Override
                    public void execute() throws Exception {
                        ftpFileProcessorService.processOperatorFile(taskId, operatorFile);
                    }

                    @Override
                    public String getTaskName() {
                        return "processOperatorFile_" + operatorFile.getSource();
                    }
                });
            }
        }

        return tasks;
    }

    /**
     * 根据执行摘要更新任务状态
     */
    private void updateTaskStatusFromSummary(String taskId, AsyncTaskManager.TaskExecutionSummary summary) {
        if (summary.isAllSuccess()) {
            updateTaskStatus(taskId, "COMPLETED");
            metricsRegistry.incrementCounter("data.notification.processed.success", "taskId", taskId);
        } else if (summary.isPartialSuccess()) {
            updateTaskStatus(taskId, "PARTIAL_SUCCESS");
            metricsRegistry.incrementCounter("data.notification.processed.partial",
                    "taskId", taskId,
                    "success", String.valueOf(summary.getSuccessTasks()),
                    "total", String.valueOf(summary.getTotalTasks()));
        } else {
            updateTaskStatus(taskId, "FAILED");
            metricsRegistry.incrementCounter("data.notification.processed.failed", "taskId", taskId);
        }

        // 记录详细的失败信息
        for (AsyncTaskManager.TaskResult result : summary.getResults()) {
            if (!result.isSuccess()) {
                log.error("流量平台-----> 任务失败详情，任务ID: {}, 子任务: {}, 错误: {}",
                        taskId, result.getTaskName(),
                        result.getException() != null ? result.getException().getMessage() : "unknown");
            }
        }
    }

    /**
     * 获取任务状态
     * 
     * @param taskId 任务ID
     * @return 任务状态
     */
    public String getTaskStatus(String taskId) {
        String status = taskStatusCache.get(taskId);
        if (status == null) {
            log.warn("流量平台-----> 任务ID不存在: {}", taskId);
            return "NOT_FOUND";
        }
        return status;
    }

    /**
     * 更新任务状态
     */
    private void updateTaskStatus(String taskId, String status) {
        taskStatusCache.put(taskId, status);
        log.debug("流量平台-----> 更新任务状态: {} -> {}", taskId, status);
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId() {
        return "TASK_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) 
                + "_" + UUID.randomUUID().toString().substring(0, 8).toUpperCase();
    }
}
