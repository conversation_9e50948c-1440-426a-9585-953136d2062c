package com.iptv.flux.service.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户分组分页查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户分组分页查询请求")
public class UserGroupQueryRequestDTO {

    @NotBlank(message = "平台标识不能为空")
    @Schema(description = "平台标识", example = "集约平台", required = true)
    private String platform;

    @Schema(description = "来源标识（运营商代码）", example = "dx", allowableValues = {"dx", "lt", "yd"})
    private String source;

    @Schema(description = "分组ID", example = "GROUP_001")
    private String groupId;

    @Schema(description = "分组名称", example = "高价值用户群")
    private String groupName;

    @Min(value = 0, message = "覆盖度最小值不能小于0")
    @Schema(description = "覆盖度最小值", example = "1000")
    private Integer coverageMin;

    @Min(value = 0, message = "覆盖度最大值不能小于0")
    @Schema(description = "覆盖度最大值", example = "100000")
    private Integer coverageMax;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "生成时间开始", example = "2024-01-01 00:00:00")
    private LocalDateTime generateTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "生成时间结束", example = "2024-01-31 23:59:59")
    private LocalDateTime generateTimeEnd;

    @Schema(description = "策略ID", example = "STRATEGY_001")
    private String strategyId;

    @Builder.Default
    @Schema(description = "是否只查询最新版本", example = "true")
    private Boolean latestOnly = true;

    @Min(value = 1, message = "页码必须大于0")
    @Builder.Default
    @Schema(description = "页码", example = "1", minimum = "1")
    private Integer page = 1;

    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    @Builder.Default
    @Schema(description = "每页大小", example = "50", minimum = "1", maximum = "1000")
    private Integer pageSize = 50;
}
