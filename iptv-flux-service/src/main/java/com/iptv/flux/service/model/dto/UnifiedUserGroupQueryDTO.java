package com.iptv.flux.service.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * 统一的用户分组查询请求DTO
 * 合并了原有的多个查询接口参数，支持向后兼容
 * 
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "统一用户分组查询请求")
public class UnifiedUserGroupQueryDTO {

    // ==================== 基础查询参数 ====================
    
    @Schema(description = "平台标识", example = "集约平台")
    private String platform;

    @Schema(description = "来源标识（运营商代码）", example = "dx", allowableValues = {"dx", "lt", "yd"})
    private String source;

    @Schema(description = "用户ID（用于单用户查询）", example = "user123")
    private String userId;

    // ==================== 分组相关参数 ====================
    
    @Schema(description = "分组ID", example = "GROUP_001")
    private String groupId;

    @Schema(description = "分组名称", example = "高价值用户群")
    private String groupName;

    @Schema(description = "策略ID（推荐使用）", example = "STRATEGY_001")
    private String strategyId;

    // ==================== 数值范围参数 ====================
    
    @Min(value = 0, message = "覆盖度最小值不能小于0")
    @Schema(description = "覆盖度最小值", example = "1000")
    private Integer coverageMin;

    @Min(value = 0, message = "覆盖度最大值不能小于0")
    @Schema(description = "覆盖度最大值", example = "100000")
    private Integer coverageMax;

    // ==================== 时间范围参数 ====================
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "生成时间开始", example = "2024-01-01 00:00:00")
    private LocalDateTime generateTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "生成时间结束", example = "2024-01-31 23:59:59")
    private LocalDateTime generateTimeEnd;

    // ==================== 查询控制参数 ====================
    
    @Builder.Default
    @Schema(description = "是否只查询最新版本", example = "true")
    private Boolean latestOnly = true;

    @Builder.Default
    @Schema(description = "是否包含详细信息", example = "false")
    private Boolean includeDetail = false;

    @Builder.Default
    @Schema(description = "输出格式", example = "json", allowableValues = {"json", "export"})
    private String format = "json";

    // ==================== 分页参数 ====================
    
    @Min(value = 1, message = "页码必须大于0")
    @Builder.Default
    @Schema(description = "页码", example = "1", minimum = "1")
    private Integer page = 1;

    @Min(value = 1, message = "每页大小必须大于0")
    @Builder.Default
    @Schema(description = "每页大小", example = "10", minimum = "1", maximum = "1000")
    private Integer pageSize = 10;

    @Min(value = 1, message = "最大限制必须大于0")
    @Builder.Default
    @Schema(description = "最大查询限制（全量查询时使用）", example = "100000")
    private Integer maxLimit = 100000;

    // ==================== 业务方法 ====================

    /**
     * 检查是否为空查询（所有查询条件都为空）
     */
    public boolean isEmpty() {
        return (platform == null || platform.trim().isEmpty()) &&
               (source == null || source.trim().isEmpty()) &&
               (userId == null || userId.trim().isEmpty()) &&
               (groupId == null || groupId.trim().isEmpty()) &&
               (groupName == null || groupName.trim().isEmpty()) &&
               (strategyId == null || strategyId.trim().isEmpty()) &&
               coverageMin == null && coverageMax == null &&
               generateTimeStart == null && generateTimeEnd == null;
    }

    /**
     * 是否为单用户查询
     */
    public boolean isSingleUserQuery() {
        return userId != null && !userId.trim().isEmpty();
    }

    /**
     * 是否为分页查询
     */
    public boolean isPagedQuery() {
        return page != null && pageSize != null;
    }

    /**
     * 是否为导出查询
     */
    public boolean isExportQuery() {
        return "export".equals(format);
    }

    /**
     * 获取有效的策略ID（优先使用strategyId，回退到businessId）
     */
    public String getEffectiveStrategyId() {
        if (strategyId != null && !strategyId.trim().isEmpty()) {
            return strategyId.trim();
        }
        return null;
    }

    /**
     * 获取查询偏移量
     */
    public int getOffset() {
        if (page == null || pageSize == null) {
            return 0;
        }
        return (page - 1) * pageSize;
    }

    /**
     * 验证分页参数
     */
    public void validatePagination() {
        if (page != null && page < 1) {
            throw new IllegalArgumentException("页码必须大于0");
        }
        if (pageSize != null && (pageSize < 1 || pageSize > 1000)) {
            throw new IllegalArgumentException("每页大小必须在1-1000之间");
        }
    }

    /**
     * 验证全量查询参数
     */
    public void validateAllQuery() {
        if (isEmpty()) {
            throw new IllegalArgumentException("全量查询必须指定至少一个筛选条件");
        }
        if (maxLimit != null && maxLimit > 100000) {
            throw new IllegalArgumentException("全量查询最大限制为100000条");
        }
    }

    /**
     * 转换为分页查询DTO（向后兼容）
     */
    public UserGroupQueryRequestDTO toPagedQueryDTO() {
        return UserGroupQueryRequestDTO.builder()
                .platform(platform)
                .source(source)
                .groupId(groupId)
                .groupName(groupName)
                .coverageMin(coverageMin)
                .coverageMax(coverageMax)
                .generateTimeStart(generateTimeStart)
                .generateTimeEnd(generateTimeEnd)
                .strategyId(strategyId)
                .latestOnly(latestOnly)
                .page(page)
                .pageSize(pageSize)
                .build();
    }

    /**
     * 转换为全量查询DTO（向后兼容）
     */
    public UserGroupQueryAllRequestDTO toAllQueryDTO() {
        return UserGroupQueryAllRequestDTO.builder()
                .platform(platform)
                .source(source)
                .groupId(groupId)
                .groupName(groupName)
                .coverageMin(coverageMin)
                .coverageMax(coverageMax)
                .generateTimeStart(generateTimeStart)
                .generateTimeEnd(generateTimeEnd)
                .strategyId(strategyId)
                .latestOnly(latestOnly)
                .maxLimit(maxLimit)
                .build();
    }
}
