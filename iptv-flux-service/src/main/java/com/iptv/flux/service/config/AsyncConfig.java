package com.iptv.flux.service.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.config
 * @className: AsyncConfig
 * @author: chiron
 * @description: 异步处理配置
 * @date: 2025/1/21 16:30
 * @version: 1.0
 */
@Configuration
@EnableAsync
@Slf4j
public class AsyncConfig {

    @Value("${user-group.data-import.async.core-pool-size:10}")
    private int dataImportCorePoolSize;

    @Value("${user-group.data-import.async.max-pool-size:20}")
    private int dataImportMaxPoolSize;

    @Value("${user-group.data-import.async.queue-capacity:500}")
    private int dataImportQueueCapacity;

    @Value("${user-group.data-import.async.keep-alive:300}")
    private int dataImportKeepAliveSeconds;

    @Value("${async.data-process.core-pool-size:4}")
    private int dataProcessCorePoolSize;

    @Value("${async.data-process.max-pool-size:8}")
    private int dataProcessMaxPoolSize;

    @Value("${async.data-process.queue-capacity:100}")
    private int dataProcessQueueCapacity;

    @Value("${async.file-download.core-pool-size:8}")
    private int fileDownloadCorePoolSize;

    @Value("${async.file-download.max-pool-size:16}")
    private int fileDownloadMaxPoolSize;

    @Value("${async.file-download.queue-capacity:200}")
    private int fileDownloadQueueCapacity;

    /**
     * 数据导入异步执行器
     */
    @Bean("dataImportExecutor")
    public Executor dataImportExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();

        // 核心线程数 - 增加以支持更多并发导入
        executor.setCorePoolSize(dataImportCorePoolSize);

        // 最大线程数 - 增加以处理大量数据
        executor.setMaxPoolSize(dataImportMaxPoolSize);

        // 队列容量 - 增加以缓冲更多任务
        executor.setQueueCapacity(dataImportQueueCapacity);

        // 线程保持活跃时间 - 增加以减少线程创建销毁开销
        executor.setKeepAliveSeconds(dataImportKeepAliveSeconds);

        // 线程名前缀
        executor.setThreadNamePrefix("DataImport-");

        // 拒绝策略：由调用线程处理
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待所有任务结束后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);

        // 等待时间 - 增加以确保大数据量处理完成
        executor.setAwaitTerminationSeconds(600);

        executor.initialize();

        log.info("流量平台-----> 数据导入异步执行器初始化完成，核心线程数: {}，最大线程数: {}，队列容量: {}",
                dataImportCorePoolSize, dataImportMaxPoolSize, dataImportQueueCapacity);
        return executor;
    }

    /**
     * 数据处理线程池
     * 用于处理数据更新通知的主要业务逻辑
     */
    @Bean("dataProcessExecutor")
    public Executor dataProcessExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(dataProcessCorePoolSize);
        executor.setMaxPoolSize(dataProcessMaxPoolSize);
        executor.setQueueCapacity(dataProcessQueueCapacity);
        executor.setThreadNamePrefix("DataProcess-");
        executor.setKeepAliveSeconds(60);

        // 拒绝策略：调用者运行，确保任务不丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());

        // 等待任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);

        executor.initialize();

        log.info("流量平台-----> 数据处理线程池初始化完成: core={}, max={}, queue={}",
                dataProcessCorePoolSize, dataProcessMaxPoolSize, dataProcessQueueCapacity);

        return executor;
    }

    /**
     * 文件下载线程池
     * 用于并行下载和处理FTP文件
     */
    @Bean("fileDownloadExecutor")
    public Executor fileDownloadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(fileDownloadCorePoolSize);
        executor.setMaxPoolSize(fileDownloadMaxPoolSize);
        executor.setQueueCapacity(fileDownloadQueueCapacity);
        executor.setThreadNamePrefix("FileDownload-");
        executor.setKeepAliveSeconds(30);

        // 拒绝策略：抛出异常，避免文件处理任务丢失
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());

        // 等待任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(30);

        executor.initialize();

        log.info("流量平台-----> 文件下载线程池初始化完成: core={}, max={}, queue={}",
                fileDownloadCorePoolSize, fileDownloadMaxPoolSize, fileDownloadQueueCapacity);

        return executor;
    }
}
