package com.iptv.flux.service.util;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.util
 * @className: LockUtils
 * @author: chiron
 * @description: TODO
 * @date: 2025/8/12 10:33
 * @version: 1.0
 */
@Slf4j
public class LockUtils {
    /**
     * 安全释放分布式锁
     * @param lock 要释放的锁
     * @param lockName 锁名称（用于日志）
     * @param metricsRegistry 指标注册器
     */
    public static void safeUnlock(RLock lock, String lockName, MetricsRegistryUtil metricsRegistry) {
        if (lock == null) {
            return;
        }

        try {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.debug("流量平台-----> 成功释放分布式锁: {}", lockName);
            } else {
                log.warn("流量平台-----> 尝试释放非当前线程持有的锁: {}", lockName);
                if (metricsRegistry != null) {
                    metricsRegistry.incrementCounter(UserGroupConstants.LOCK_RELEASE_WRONG_THREAD_METRIC, "lockName", lockName);
                }
            }
        } catch (IllegalMonitorStateException e) {
            log.warn("流量平台-----> 锁已被释放或过期: {}, 错误: {}", lockName, e.getMessage());
            if (metricsRegistry != null) {
                metricsRegistry.incrementCounter(UserGroupConstants.LOCK_RELEASE_ALREADY_RELEASED_METRIC, "lockName", lockName);
            }
        } catch (Exception e) {
            log.error("流量平台-----> 释放分布式锁失败: {}, 错误: {}", lockName, e.getMessage(), e);
            if (metricsRegistry != null) {
                metricsRegistry.incrementCounter(UserGroupConstants.LOCK_RELEASE_ERROR_METRIC, "lockName", lockName, "error", e.getClass().getSimpleName());
            }
        }
    }
}
