package com.iptv.flux.service.model.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.model.entity
 * @className: GroupInfo
 * @author: chiron
 * @description: TODO
 * @date: 2025/2/28 12:59
 * @version: 1.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupInfo {
    private Long id;
    private String groupId;
    private String businessId;
    private String groupName;
    private String description;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
