package com.iptv.flux.service.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一的分组查询请求DTO
 * 合并了原有的 /groups、/groups/{source} 和 /groups/query 接口参数
 * 
 * <AUTHOR>
 * @date 2025/01/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "统一分组查询请求")
public class UnifiedGroupQueryDTO {

    @Schema(description = "分组名称", example = "VIP用户组")
    private String groupName;

    @Schema(description = "策略ID", example = "STRATEGY_001")
    private String strategyId;

    @Schema(description = "来源", example = "dx", allowableValues = {"dx", "yd", "lt", "other"})
    private String source;

    @Schema(description = "分组ID", example = "GROUP_001")
    private String groupId;

    @Schema(description = "平台标识", example = "集约平台")
    private String platform;

    @Schema(description = "分组描述", example = "高价值用户群体")
    private String description;

    /**
     * 检查是否为空查询（所有参数都为空）
     */
    public boolean isEmpty() {
        return (groupName == null || groupName.trim().isEmpty()) &&
               (strategyId == null || strategyId.trim().isEmpty()) &&
               (source == null || source.trim().isEmpty()) &&
               (groupId == null || groupId.trim().isEmpty()) &&
               (platform == null || platform.trim().isEmpty()) &&
               (description == null || description.trim().isEmpty());
    }

    /**
     * 获取非空的分组名称
     */
    public String getTrimmedGroupName() {
        return groupName != null ? groupName.trim() : null;
    }

    /**
     * 获取非空的来源
     */
    public String getTrimmedSource() {
        return source != null ? source.trim() : null;
    }

    /**
     * 获取非空的策略ID
     */
    public String getTrimmedStrategyId() {
        return strategyId != null ? strategyId.trim() : null;
    }

    /**
     * 是否为按来源查询（兼容原 /groups/{source} 接口）
     */
    public boolean isSourceOnlyQuery() {
        return (source != null && !source.trim().isEmpty()) &&
               (groupName == null || groupName.trim().isEmpty()) &&
               (strategyId == null || strategyId.trim().isEmpty()) &&
               (groupId == null || groupId.trim().isEmpty());
    }

    /**
     * 转换为GroupQueryRequestDTO
     */
    public com.iptv.flux.common.dto.GroupQueryRequestDTO toGroupQueryRequestDTO() {
        return com.iptv.flux.common.dto.GroupQueryRequestDTO.builder()
                .groupName(groupName)
                .strategyId(strategyId)
                .source(source)
                .build();
    }

    /**
     * 创建查询所有分组的实例（兼容原 /groups 接口）
     */
    public static UnifiedGroupQueryDTO forAllGroups() {
        return UnifiedGroupQueryDTO.builder().build();
    }

    /**
     * 创建按来源查询的实例（兼容原 /groups/{source} 接口）
     */
    public static UnifiedGroupQueryDTO forSource(String source) {
        return UnifiedGroupQueryDTO.builder()
                .source(source)
                .build();
    }

    /**
     * 创建条件查询的实例（兼容原 /groups/query 接口）
     */
    public static UnifiedGroupQueryDTO forConditions(String groupName, String strategyId, String source) {
        return UnifiedGroupQueryDTO.builder()
                .groupName(groupName)
                .strategyId(strategyId)
                .source(source)
                .build();
    }
}
