package com.iptv.flux.service.repository;

import com.iptv.flux.common.dto.GroupInfoDTO;
import com.iptv.flux.common.dto.GroupQueryRequestDTO;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.model.entity.GroupInfo;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.jooq.Record;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.jooq.impl.DSL.field;
import static org.jooq.impl.DSL.table;

/**
 * <AUTHOR>
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class GroupInfoRepository {

    private final DSLContext dsl;
    private final MetricsRegistryUtil metricsRegistry;
    private final UserGroupRepository userGroupRepository;
    private static final Table<?> GROUP_INFO_TABLE = table("group_info");
    private static final Field<String> GROUP_ID = field("group_id", String.class);
    private static final Field<String> STRATEGY_ID = field("strategy_id", String.class);
    private static final Field<String> GROUP_NAME = field("group_name", String.class);
    private static final Field<String> DESCRIPTION = field("description", String.class);
    private static final Field<String> PLATFORM = field("platform", String.class);
    private static final Field<Integer> ACTIVE = field("active", Integer.class);

    @Timed(value = "repository.group.findById", percentiles = {0.5, 0.95, 0.99})
    public GroupInfo findByGroupId(String groupId) {
        long startTime = System.currentTimeMillis();

        try {
            Record record = dsl.select()
                    .from(GROUP_INFO_TABLE)
                    .where(GROUP_ID.eq(groupId))
                    .fetchOne();

            if (record == null) {
                log.debug("找不到带有groupId的组: {}", groupId);
                return null;
            }

            log.debug("找到群组，群组ID：{}，耗时{}ms", groupId, System.currentTimeMillis() - startTime);

            return GroupInfo.builder()
                    .id(record.get(field("id", Long.class)))
                    .groupId(record.get(GROUP_ID))
                    .strategyId(record.get(STRATEGY_ID))
                    .groupName(record.get(GROUP_NAME))
                    .description(record.get(DESCRIPTION))
                    .build();
        } catch (Exception e) {
            log.error("根据groupId查找组时出错: {}", groupId, e);
            throw e;
        }
    }

    @Timed(value = "repository.group.findByIds", percentiles = {0.5, 0.95, 0.99})
    public Map<String, GroupInfoDTO> findGroupsByIds(List<String> groupIds) {
        if (groupIds == null || groupIds.isEmpty()) {
            return Collections.emptyMap();
        }

        long startTime = System.currentTimeMillis();

        try {
            Map<String, GroupInfoDTO> result = dsl.select(GROUP_ID, STRATEGY_ID, GROUP_NAME, DESCRIPTION)
                    .from(GROUP_INFO_TABLE)
                    .where(GROUP_ID.in(groupIds))
                    .fetch()
                    .stream()
                    .map(r -> GroupInfoDTO.builder()
                            .groupId(r.get(GROUP_ID))
                            .strategyId(r.get(STRATEGY_ID))
                            .groupName(r.get(GROUP_NAME))
                            .description(r.get(DESCRIPTION))
                            .build())
                    .collect(Collectors.toMap(
                            GroupInfoDTO::getGroupId,
                            Function.identity()
                    ));

            log.debug("找到了 {} 个群组，共请求了 {} 个，耗时 {}ms",
                    result.size(), groupIds.size(), System.currentTimeMillis() - startTime);

            return result;
        } catch (Exception e) {
            log.error("按ID查找组时出错，数量: {}", groupIds.size(), e);
            metricsRegistry.incrementCounter("repository.error", "method", "findGroupsByIds");
            throw e;
        }
    }

    @Timed(value = "repository.group.save", percentiles = {0.5, 0.95, 0.99})
    public void saveGroupInfo(GroupInfo groupInfo) {
        long startTime = System.currentTimeMillis();

        try {
            int count = dsl.insertInto(GROUP_INFO_TABLE)
                    .set(GROUP_ID, groupInfo.getGroupId())
                    .set(STRATEGY_ID, groupInfo.getStrategyId())
                    .set(GROUP_NAME, groupInfo.getGroupName())
                    .set(DESCRIPTION, groupInfo.getDescription())
                    .onDuplicateKeyUpdate()
                    .set(STRATEGY_ID, groupInfo.getStrategyId())
                    .set(GROUP_NAME, groupInfo.getGroupName())
                    .set(DESCRIPTION, groupInfo.getDescription())
                    .execute();

            log.debug("保存了 groupId: {} 的群组信息，受影响的行数: {}，耗时 {}ms",
                    groupInfo.getGroupId(), count, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("保存groupId的组信息时出错: {}", groupInfo.getGroupId(), e);
            metricsRegistry.incrementCounter("repository.error", "method", "saveGroupInfo");
            throw e;
        }
    }

    /**
     * 查询所有分组信息
     *
     * @return 分组信息列表
     */
    @Timed(value = "repository.group.findAllGroups", percentiles = {0.5, 0.95, 0.99})
    public List<GroupInfoDTO> findAllGroups() {
        long startTime = System.currentTimeMillis();

        try {
            List<GroupInfoDTO> result = dsl.select(GROUP_ID, BUSINESS_ID, GROUP_NAME, DESCRIPTION)
                    .from(GROUP_INFO_TABLE)
                    .orderBy(GROUP_NAME)
                    .fetch()
                    .map(r -> GroupInfoDTO.builder()
                            .groupId(r.get(GROUP_ID))
                            .businessId(r.get(BUSINESS_ID))
                            .groupName(r.get(GROUP_NAME))
                            .description(r.get(DESCRIPTION))
                            .build());

            log.debug("流量平台-----> 查询到 {} 个分组信息，耗时 {}ms",
                    result.size(), System.currentTimeMillis() - startTime);

            return result;
        } catch (Exception e) {
            log.error("流量平台-----> 查询所有分组信息失败", e);
            metricsRegistry.incrementCounter("repository.error", "method", "findAllGroups");
            return Collections.emptyList();
        }
    }

    /**
     * 根据来源获取分组信息
     *
     * @param source 来源/运营商
     * @return 分组信息列表
     */
    @Timed(value = "service.groupinfo.getBySource", percentiles = {0.5, 0.95, 0.99})
    public List<GroupInfoDTO> getGroupsBySource(String source) {
        long startTime = System.currentTimeMillis();

        log.debug("流量平台-----> 获取来源为 {} 的分组信息", source);

        // 1. 先获取该来源下的所有分组ID
        Set<String> groupIds = userGroupRepository.findGroupIdsBySource(source);

        if (groupIds.isEmpty()) {
            log.debug("流量平台-----> 来源 {} 没有关联的分组", source);
            return Collections.emptyList();
        }

        // 2. 根据分组ID获取分组详情
        Map<String, GroupInfoDTO> groupInfoMap = findGroupsByIds(new ArrayList<>(groupIds));

        // 3. 转换为列表返回
        List<GroupInfoDTO> result = new ArrayList<>(groupInfoMap.values());

        log.debug("流量平台-----> 获取到来源 {} 的 {} 个分组，耗时 {}ms",
                source, result.size(), System.currentTimeMillis() - startTime);

        return result;
    }

    /**
     * 根据条件查询分组信息
     *
     * @param request 查询条件
     * @return 分组信息列表
     */
    @Timed(value = "repository.group.queryByConditions", percentiles = {0.5, 0.95, 0.99})
    public List<GroupInfoDTO> queryGroupsByConditions(GroupQueryRequestDTO request) {
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 开始根据条件查询分组");

        try {
            // 构建查询条件
            Condition condition = buildQueryCondition(request);

            // 执行查询
            Result<Record> records = dsl.select()
                    .from(GROUP_INFO_TABLE)
                    .where(condition)
                    .orderBy(GROUP_NAME.asc()) // 按分组名称排序
                    .fetch();

            // 转换为DTO列表
            List<GroupInfoDTO> result = new ArrayList<>();
            for (Record record : records) {
                GroupInfoDTO dto = convertRecordToDTO(record);
                result.add(dto);
            }

            long duration = System.currentTimeMillis() - startTime;
            log.debug("流量平台-----> 条件查询分组完成，结果数量: {}, 耗时: {}ms", result.size(), duration);

            return result;

        } catch (Exception e) {
            long duration = System.currentTimeMillis() - startTime;
            log.error("流量平台-----> 条件查询分组失败，耗时: {}ms", duration, e);
            throw e;
        }
    }

    /**
     * 构建查询条件
     */
    private Condition buildQueryCondition(GroupQueryRequestDTO request) {
        Condition condition = DSL.trueCondition(); // 默认为 true，即无条件

        // 分组名称条件（模糊查询）
        if (request.getTrimmedGroupName() != null && !request.getTrimmedGroupName().isEmpty()) {
            condition = condition.and(GROUP_NAME.like("%" + request.getTrimmedGroupName() + "%"));
            log.debug("流量平台-----> 添加分组名称查询条件: {}", request.getTrimmedGroupName());
        }

        // 策略ID条件（精确查询）
        if (request.getTrimmedStrategyId() != null && !request.getTrimmedStrategyId().isEmpty()) {
            condition = condition.and(STRATEGY_ID.eq(request.getTrimmedStrategyId()));
            log.debug("流量平台-----> 添加策略ID查询条件: {}", request.getTrimmedStrategyId());
        }

        // 来源条件（这里假设 group_info 表中有 source 字段，如果没有需要调整）
        if (request.getTrimmedSource() != null && !request.getTrimmedSource().isEmpty()) {
            // 注意：如果 group_info 表中没有 source 字段，这里需要通过关联查询实现
            // 暂时注释掉，因为原始表结构中可能没有 source 字段
            // condition = condition.and(field("source", String.class).eq(request.getTrimmedSource()));
            log.debug("流量平台-----> 来源查询条件暂时跳过（需要确认表结构）: {}", request.getTrimmedSource());
        }

        return condition;
    }

    /**
     * 将数据库记录转换为DTO
     */
    private GroupInfoDTO convertRecordToDTO(Record record) {
        return GroupInfoDTO.builder()
                .groupId(record.get(GROUP_ID))
                .strategyId(record.get(STRATEGY_ID))
                .groupName(record.get(GROUP_NAME))
                .description(record.get(DESCRIPTION))
                .build();
    }

    /**
     * 更新分组的扩展信息（平台等）
     *
     * @param groupId 分组ID
     * @param platform 平台标识
     */
    @Timed(value = "repository.group.updateExtendedInfo", percentiles = {0.5, 0.95, 0.99})
    public void updateGroupExtendedInfo(String groupId, String platform) {
        long startTime = System.currentTimeMillis();

        try {
            int count = dsl.update(GROUP_INFO_TABLE)
                    .set(PLATFORM, platform)
                    .set(ACTIVE, 1) // 设置为激活状态
                    .where(GROUP_ID.eq(groupId))
                    .execute();

            log.debug("流量平台-----> 更新分组扩展信息: groupId={}, platform={}, 影响行数: {}, 耗时: {}ms",
                    groupId, platform, count, System.currentTimeMillis() - startTime);

        } catch (Exception e) {
            log.error("流量平台-----> 更新分组扩展信息失败: groupId={}", groupId, e);
            metricsRegistry.incrementCounter("repository.error", "method", "updateGroupExtendedInfo");
            throw e;
        }
    }

}