package com.iptv.flux.service.service;

import com.iptv.flux.common.dto.PagedResponseDTO;
import com.iptv.flux.common.utils.JsonUtils;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.model.dto.UserGroupQueryAllRequestDTO;
import com.iptv.flux.service.model.dto.UserGroupQueryRequestDTO;
import com.iptv.flux.service.model.dto.UserGroupQueryResponseDTO;
import com.iptv.flux.service.repository.UserGroupQueryRepository;
import io.micrometer.core.annotation.Timed;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.PrintWriter;
import java.util.List;

/**
 * 用户分组查询服务
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class UserGroupQueryService {

    private final UserGroupQueryRepository userGroupQueryRepository;
    private final UserGroupCacheService cacheService;
    private final MetricsRegistryUtil metricsRegistry;

    /**
     * 分页查询用户分组
     */
    @Timed(value = "service.usergroup.query.page", percentiles = {0.5, 0.95, 0.99})
    public PagedResponseDTO<UserGroupQueryResponseDTO> queryUserGroupsWithPaging(UserGroupQueryRequestDTO request) {
        long startTime = System.currentTimeMillis();
        
        log.debug("流量平台-----> 开始分页查询用户分组: platform={}, page={}, pageSize={}", 
                request.getPlatform(), request.getPage(), request.getPageSize());

        try {
            // 1. 构建缓存键
            String cacheKey = buildCacheKey(request);
            
            // 2. 尝试从缓存获取
            PagedResponseDTO<UserGroupQueryResponseDTO> cachedResult = cacheService.getCachedPagedResult(cacheKey);
            if (cachedResult != null) {
                log.debug("流量平台-----> 分页查询缓存命中: {}", cacheKey);
                metricsRegistry.incrementCounter("usergroup.query.cache.hit", "type", "page");
                return cachedResult;
            }
            
            // 3. 查询数据库
            PagedResponseDTO<UserGroupQueryResponseDTO> result = userGroupQueryRepository.queryWithPaging(request);
            
            // 4. 更新缓存
            cacheService.cachePagedResult(cacheKey, result);
            
            // 5. 记录指标
            metricsRegistry.incrementCounter("usergroup.query.cache.miss", "type", "page");
            metricsRegistry.recordExecutionTime("usergroup.query.page.duration", startTime);
            
            log.debug("流量平台-----> 分页查询完成，耗时: {}ms, 返回: {} 条记录",
                    System.currentTimeMillis() - startTime, result.getList().size());
            
            return result;
            
        } catch (Exception e) {
            metricsRegistry.incrementCounter("usergroup.query.error", 
                    "type", "page", "error", e.getClass().getSimpleName());
            log.error("流量平台-----> 分页查询用户分组失败", e);
            throw new RuntimeException("分页查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 全量查询用户分组
     */
    @Timed(value = "service.usergroup.query.all", percentiles = {0.5, 0.95, 0.99})
    public List<UserGroupQueryResponseDTO> queryAllUserGroups(UserGroupQueryAllRequestDTO request) {
        long startTime = System.currentTimeMillis();
        
        log.debug("流量平台-----> 开始全量查询用户分组: platform={}, maxLimit={}", 
                request.getPlatform(), request.getMaxLimit());

        try {
            // 1. 参数校验
            validateAllQueryRequest(request);
            
            // 2. 构建缓存键
            String cacheKey = buildCacheKeyForAll(request);
            
            // 3. 尝试从缓存获取
            List<UserGroupQueryResponseDTO> cachedResult = cacheService.getCachedAllResult(cacheKey);
            if (cachedResult != null) {
                log.debug("流量平台-----> 全量查询缓存命中: {}", cacheKey);
                metricsRegistry.incrementCounter("usergroup.query.cache.hit", "type", "all");
                return cachedResult;
            }
            
            // 4. 查询数据库
            List<UserGroupQueryResponseDTO> result = userGroupQueryRepository.queryAll(request);
            
            // 5. 更新缓存
            cacheService.cacheAllResult(cacheKey, result);
            
            // 6. 记录指标
            metricsRegistry.incrementCounter("usergroup.query.cache.miss", "type", "all");
            metricsRegistry.recordExecutionTime("usergroup.query.all.duration", startTime);
            
            log.info("流量平台-----> 全量查询完成，耗时: {}ms, 返回: {} 条记录", 
                    System.currentTimeMillis() - startTime, result.size());
            
            return result;
            
        } catch (Exception e) {
            metricsRegistry.incrementCounter("usergroup.query.error", 
                    "type", "all", "error", e.getClass().getSimpleName());
            log.error("流量平台-----> 全量查询用户分组失败", e);
            throw new RuntimeException("全量查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 流式导出用户分组
     */
    @Timed(value = "service.usergroup.export", percentiles = {0.5, 0.95, 0.99})
    public void exportUserGroups(UserGroupQueryAllRequestDTO request, PrintWriter writer) {
        long startTime = System.currentTimeMillis();
        int batchSize = 1000;
        int offset = 0;
        int totalExported = 0;
        
        log.info("流量平台-----> 开始流式导出用户分组: platform={}", request.getPlatform());

        try {
            // 1. 参数校验
            validateAllQueryRequest(request);
            
            writer.println("{\"data\":[");
            
            boolean firstBatch = true;
            List<UserGroupQueryResponseDTO> batch;
            
            do {
                // 2. 分批查询
                batch = userGroupQueryRepository.queryBatch(request, offset, batchSize);
                
                for (int i = 0; i < batch.size(); i++) {
                    if (!firstBatch || i > 0) {
                        writer.print(",");
                    }
                    writer.print(JsonUtils.toJson(batch.get(i)));
                    firstBatch = false;
                }
                
                offset += batchSize;
                totalExported += batch.size();
                
                // 3. 刷新输出流
                writer.flush();
                
                log.debug("流量平台-----> 导出进度: {} 条记录", totalExported);
                
            } while (batch.size() == batchSize && totalExported < request.getMaxLimit());
            
            writer.println("],\"total\":" + totalExported + "}");
            
            // 4. 记录指标
            metricsRegistry.recordExecutionTime("usergroup.export.duration", startTime);
            metricsRegistry.incrementCounter("usergroup.export.success", 
                    "platform", request.getPlatform());
            
            log.info("流量平台-----> 流式导出完成，导出 {} 条记录，耗时: {}ms", 
                    totalExported, System.currentTimeMillis() - startTime);
            
        } catch (Exception e) {
            metricsRegistry.incrementCounter("usergroup.export.error", 
                    "platform", request.getPlatform(), "error", e.getClass().getSimpleName());
            log.error("流量平台-----> 流式导出用户分组失败", e);
            throw new RuntimeException("导出失败: " + e.getMessage(), e);
        }
    }

    /**
     * 校验全量查询请求
     */
    private void validateAllQueryRequest(UserGroupQueryAllRequestDTO request) {
        // 全量查询必须有一定的限制条件，防止数据量过大
        if (request.getSource() == null &&
            request.getGroupId() == null &&
            request.getGroupName() == null &&
            request.getGenerateTimeStart() == null &&
            request.getStrategyId() == null) {
            throw new IllegalArgumentException("全量查询必须指定至少一个筛选条件");
        }
        
        if (request.getMaxLimit() > 100000) {
            throw new IllegalArgumentException("全量查询最大限制为100000条");
        }
    }

    /**
     * 构建分页查询缓存键
     */
    private String buildCacheKey(UserGroupQueryRequestDTO request) {
        return String.format("usergroup:page:%s:%s:%s:%s:%s:%s:%s:%s:%s:%s:%s:%s", 
                request.getPlatform(),
                request.getSource() != null ? request.getSource() : "null",
                request.getGroupId() != null ? request.getGroupId() : "null",
                request.getGroupName() != null ? request.getGroupName() : "null",
                request.getCoverageMin() != null ? request.getCoverageMin() : "null",
                request.getCoverageMax() != null ? request.getCoverageMax() : "null",
                request.getGenerateTimeStart() != null ? request.getGenerateTimeStart().toString() : "null",
                request.getGenerateTimeEnd() != null ? request.getGenerateTimeEnd().toString() : "null",
                request.getStrategyId() != null ? request.getStrategyId() : "null",
                request.getLatestOnly(),
                request.getPage(),
                request.getPageSize());
    }

    /**
     * 构建全量查询缓存键
     */
    private String buildCacheKeyForAll(UserGroupQueryAllRequestDTO request) {
        return String.format("usergroup:all:%s:%s:%s:%s:%s:%s:%s:%s:%s:%s:%s", 
                request.getPlatform(),
                request.getSource() != null ? request.getSource() : "null",
                request.getGroupId() != null ? request.getGroupId() : "null",
                request.getGroupName() != null ? request.getGroupName() : "null",
                request.getCoverageMin() != null ? request.getCoverageMin() : "null",
                request.getCoverageMax() != null ? request.getCoverageMax() : "null",
                request.getGenerateTimeStart() != null ? request.getGenerateTimeStart().toString() : "null",
                request.getGenerateTimeEnd() != null ? request.getGenerateTimeEnd().toString() : "null",
                request.getStrategyId() != null ? request.getStrategyId() : "null",
                request.getLatestOnly(),
                request.getMaxLimit());
    }
}
