package com.iptv.flux.service.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 数据更新通知DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "数据更新通知信息")
public class UpdateNotificationDTO {

    @NotNull(message = "更新时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "数据更新时间", example = "2024-01-01 12:00:00")
    private LocalDateTime updateTime;

    @NotBlank(message = "groups文件URL不能为空")
    @Schema(description = "groups.txt文件URL", example = "ftp://***********/data/groups.txt")
    private String groupsFileUrl;

    @NotBlank(message = "strategies文件URL不能为空")
    @Schema(description = "strategies.txt文件URL", example = "ftp://***********/data/strategies.txt")
    private String strategiesFileUrl;

    @NotBlank(message = "userGroup文件URL不能为空")
    @Schema(description = "userGroup.txt文件URL", example = "ftp://***********/data/userGroup.txt")
    private String userGroupFileUrl;

    @Valid
    @Schema(description = "分运营商文件URL列表")
    private List<OperatorFileDTO> operatorFiles;

    @Schema(description = "通知来源", example = "集约平台")
    private String source;

    @Schema(description = "备注信息")
    private String remark;
}
