package com.iptv.flux.service.controller;

import com.iptv.flux.common.dto.ResultDTO;
import com.iptv.flux.common.swagger.SwaggerAnnotations;
import com.iptv.flux.service.model.dto.UpdateNotificationDTO;
import com.iptv.flux.service.service.DataNotificationService;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 数据通知接收控制器
 * 用于接收FTP文件路径推送通知
 */
@RestController
@RequestMapping("/api/data-notification")
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "数据通知管理", description = "FTP数据文件通知接收接口")
public class DataNotificationController {

    private final DataNotificationService dataNotificationService;

    /**
     * 接收数据更新通知
     *
     * @param notification 更新通知信息
     * @return 处理结果
     */
    @PostMapping(value = "/update", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.data.notification.update", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.SaveOperation(
            summary = "接收数据更新通知",
            description = "接收FTP文件路径推送通知，触发数据下载和处理流程"
    )
    public ResultDTO<String> receiveUpdateNotification(@RequestBody @Valid UpdateNotificationDTO notification) {
        log.info("流量平台-----> 收到数据更新通知: updateTime={}, groupsFile={}, strategiesFile={}, userGroupFile={}, operatorFiles={}",
                notification.getUpdateTime(),
                notification.getGroupsFileUrl(),
                notification.getStrategiesFileUrl(),
                notification.getUserGroupFileUrl(),
                notification.getOperatorFiles() != null ? notification.getOperatorFiles().size() : 0);

        try {
            String taskId = dataNotificationService.processUpdateNotification(notification);
            log.info("流量平台-----> 数据更新通知处理成功，任务ID: {}", taskId);
            return ResultDTO.success(taskId);
        } catch (Exception e) {
            log.error("流量平台-----> 处理数据更新通知失败", e);
            return ResultDTO.fail("通知处理失败: " + e.getMessage());
        }
    }

    /**
     * 查询处理任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    @GetMapping(value = "/task/{taskId}/status", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.data.notification.task.status", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "查询处理任务状态",
            description = "根据任务ID查询数据处理任务的执行状态",
            responseType = String.class
    )
    public ResultDTO<String> getTaskStatus(@PathVariable String taskId) {
        log.debug("流量平台-----> 查询任务状态: {}", taskId);

        try {
            String status = dataNotificationService.getTaskStatus(taskId);
            return ResultDTO.success(status);
        } catch (Exception e) {
            log.error("流量平台-----> 查询任务状态失败: {}", taskId, e);
            return ResultDTO.fail("查询失败: " + e.getMessage());
        }
    }
}
