package com.iptv.flux.service.service;

import com.iptv.flux.common.utils.MetricsRegistryUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

/**
 * 异步任务管理器
 * 负责异步任务的执行、异常处理、超时控制和资源回收
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class AsyncTaskManager {

    private final MetricsRegistryUtil metricsRegistry;

    @Qualifier("fileDownloadExecutor")
    private final Executor fileDownloadExecutor;

    @Value("${async.task.timeout:300000}") // 5分钟超时
    private long taskTimeoutMs;

    @Value("${async.task.max-retry:3}")
    private int maxRetryCount;

    /**
     * 异步任务结果
     */
    public static class TaskResult {
        private final String taskName;
        private final boolean success;
        private final Exception exception;
        private final long executionTimeMs;

        public TaskResult(String taskName, boolean success, Exception exception, long executionTimeMs) {
            this.taskName = taskName;
            this.success = success;
            this.exception = exception;
            this.executionTimeMs = executionTimeMs;
        }

        public String getTaskName() { return taskName; }
        public boolean isSuccess() { return success; }
        public Exception getException() { return exception; }
        public long getExecutionTimeMs() { return executionTimeMs; }
    }

    /**
     * 异步任务执行结果汇总
     */
    public static class TaskExecutionSummary {
        private final List<TaskResult> results;
        private final int totalTasks;
        private final int successTasks;
        private final int failedTasks;
        private final long totalExecutionTimeMs;

        public TaskExecutionSummary(List<TaskResult> results, long totalExecutionTimeMs) {
            this.results = new ArrayList<>(results);
            this.totalTasks = results.size();
            this.successTasks = (int) results.stream().filter(TaskResult::isSuccess).count();
            this.failedTasks = totalTasks - successTasks;
            this.totalExecutionTimeMs = totalExecutionTimeMs;
        }

        public List<TaskResult> getResults() { return results; }
        public int getTotalTasks() { return totalTasks; }
        public int getSuccessTasks() { return successTasks; }
        public int getFailedTasks() { return failedTasks; }
        public long getTotalExecutionTimeMs() { return totalExecutionTimeMs; }
        public boolean isAllSuccess() { return failedTasks == 0; }
        public boolean isPartialSuccess() { return successTasks > 0 && failedTasks > 0; }
        public boolean isAllFailed() { return successTasks == 0; }
    }

    /**
     * 执行多个异步任务，带完善的异常处理和资源管理
     * 
     * @param taskId 任务ID
     * @param tasks 任务列表
     * @return 执行结果汇总
     */
    public TaskExecutionSummary executeTasksWithManagement(String taskId, List<AsyncTask> tasks) {
        long startTime = System.currentTimeMillis();
        List<CompletableFuture<TaskResult>> futures = new ArrayList<>();
        
        log.info("流量平台-----> 开始执行异步任务组，任务ID: {}, 任务数量: {}", taskId, tasks.size());

        try {
            // 1. 提交所有任务
            for (AsyncTask task : tasks) {
                CompletableFuture<TaskResult> future = executeTaskWithRetry(taskId, task);
                futures.add(future);
            }

            // 2. 等待所有任务完成（带超时控制）
            CompletableFuture<Void> allTasks = CompletableFuture.allOf(
                    futures.toArray(new CompletableFuture[0]));

            try {
                allTasks.get(taskTimeoutMs, TimeUnit.MILLISECONDS);
            } catch (TimeoutException e) {
                log.warn("流量平台-----> 任务组执行超时，任务ID: {}, 超时时间: {}ms", taskId, taskTimeoutMs);
                
                // 取消未完成的任务
                cancelUnfinishedTasks(futures);
                
                // 记录超时指标
                metricsRegistry.incrementCounter("async.task.timeout", "taskId", taskId);
            }

            // 3. 收集所有任务结果
            List<TaskResult> results = collectTaskResults(futures);
            
            long totalExecutionTime = System.currentTimeMillis() - startTime;
            TaskExecutionSummary summary = new TaskExecutionSummary(results, totalExecutionTime);

            // 4. 记录执行指标
            recordExecutionMetrics(taskId, summary);

            log.info("流量平台-----> 异步任务组执行完成，任务ID: {}, 总数: {}, 成功: {}, 失败: {}, 耗时: {}ms",
                    taskId, summary.getTotalTasks(), summary.getSuccessTasks(), 
                    summary.getFailedTasks(), summary.getTotalExecutionTimeMs());

            return summary;

        } catch (Exception e) {
            log.error("流量平台-----> 异步任务组执行异常，任务ID: {}", taskId, e);
            
            // 确保所有任务被取消
            cancelUnfinishedTasks(futures);
            
            // 记录异常指标
            metricsRegistry.incrementCounter("async.task.group.error", 
                    "taskId", taskId, "error", e.getClass().getSimpleName());
            
            throw new RuntimeException("异步任务组执行失败", e);
        }
    }

    /**
     * 执行单个任务（带重试机制）
     */
    private CompletableFuture<TaskResult> executeTaskWithRetry(String taskId, AsyncTask task) {
        return CompletableFuture.supplyAsync(new Supplier<TaskResult>() {
            @Override
            public TaskResult get() {
                String taskName = task.getTaskName();
                long taskStartTime = System.currentTimeMillis();
                Exception lastException = null;

                // 重试机制
                for (int attempt = 1; attempt <= maxRetryCount; attempt++) {
                    try {
                        log.debug("流量平台-----> 执行任务: {}, 尝试次数: {}/{}", taskName, attempt, maxRetryCount);
                        
                        // 执行任务
                        task.execute();
                        
                        long executionTime = System.currentTimeMillis() - taskStartTime;
                        log.debug("流量平台-----> 任务执行成功: {}, 耗时: {}ms", taskName, executionTime);
                        
                        // 记录成功指标
                        metricsRegistry.incrementCounter("async.task.success", 
                                "taskId", taskId, "taskName", taskName, "attempt", String.valueOf(attempt));
                        
                        return new TaskResult(taskName, true, null, executionTime);
                        
                    } catch (Exception e) {
                        lastException = e;
                        log.warn("流量平台-----> 任务执行失败: {}, 尝试次数: {}/{}, 错误: {}", 
                                taskName, attempt, maxRetryCount, e.getMessage());
                        
                        // 记录重试指标
                        metricsRegistry.incrementCounter("async.task.retry", 
                                "taskId", taskId, "taskName", taskName, "attempt", String.valueOf(attempt));
                        
                        // 如果不是最后一次尝试，等待一段时间再重试
                        if (attempt < maxRetryCount) {
                            try {
                                Thread.sleep(1000 * attempt); // 递增等待时间
                            } catch (InterruptedException ie) {
                                Thread.currentThread().interrupt();
                                break;
                            }
                        }
                    }
                }

                // 所有重试都失败
                long executionTime = System.currentTimeMillis() - taskStartTime;
                log.error("流量平台-----> 任务最终失败: {}, 总耗时: {}ms", taskName, executionTime, lastException);
                
                // 记录失败指标
                metricsRegistry.incrementCounter("async.task.failed", 
                        "taskId", taskId, "taskName", taskName, "error", 
                        lastException != null ? lastException.getClass().getSimpleName() : "unknown");
                
                return new TaskResult(taskName, false, lastException, executionTime);
            }
        }, fileDownloadExecutor);
    }

    /**
     * 取消未完成的任务
     */
    private void cancelUnfinishedTasks(List<CompletableFuture<TaskResult>> futures) {
        int cancelledCount = 0;
        for (CompletableFuture<TaskResult> future : futures) {
            if (!future.isDone()) {
                boolean cancelled = future.cancel(true);
                if (cancelled) {
                    cancelledCount++;
                }
            }
        }
        
        if (cancelledCount > 0) {
            log.warn("流量平台-----> 取消了 {} 个未完成的任务", cancelledCount);
            metricsRegistry.incrementCounter("async.task.cancelled", "count", String.valueOf(cancelledCount));
        }
    }

    /**
     * 收集任务结果
     */
    private List<TaskResult> collectTaskResults(List<CompletableFuture<TaskResult>> futures) {
        List<TaskResult> results = new ArrayList<>();
        
        for (CompletableFuture<TaskResult> future : futures) {
            try {
                if (future.isDone() && !future.isCancelled()) {
                    TaskResult result = future.get();
                    results.add(result);
                } else if (future.isCancelled()) {
                    results.add(new TaskResult("cancelled", false, 
                            new CancellationException("Task was cancelled"), 0));
                } else {
                    results.add(new TaskResult("timeout", false, 
                            new TimeoutException("Task timed out"), 0));
                }
            } catch (Exception e) {
                results.add(new TaskResult("exception", false, e, 0));
            }
        }
        
        return results;
    }

    /**
     * 记录执行指标
     */
    private void recordExecutionMetrics(String taskId, TaskExecutionSummary summary) {
        metricsRegistry.recordTimer("async.task.group.duration", summary.getTotalExecutionTimeMs());
        metricsRegistry.incrementCounter("async.task.group.completed", 
                "taskId", taskId, 
                "total", String.valueOf(summary.getTotalTasks()),
                "success", String.valueOf(summary.getSuccessTasks()),
                "failed", String.valueOf(summary.getFailedTasks()));
    }

    /**
     * 异步任务接口
     */
    @FunctionalInterface
    public interface AsyncTask {
        void execute() throws Exception;
        
        default String getTaskName() {
            return this.getClass().getSimpleName();
        }
    }
}
