package com.iptv.flux.service.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户分组全量查询请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户分组全量查询请求")
public class UserGroupQueryAllRequestDTO {

    @NotBlank(message = "平台标识不能为空")
    @Schema(description = "平台标识", example = "集约平台", required = true)
    private String platform;

    @Schema(description = "来源标识（运营商代码）", example = "dx", allowableValues = {"dx", "lt", "yd"})
    private String source;

    @Schema(description = "分组ID", example = "GROUP_001")
    private String groupId;

    @Schema(description = "分组名称", example = "高价值用户群")
    private String groupName;

    @Min(value = 0, message = "覆盖度最小值不能小于0")
    @Schema(description = "覆盖度最小值", example = "1000")
    private Integer coverageMin;

    @Min(value = 0, message = "覆盖度最大值不能小于0")
    @Schema(description = "覆盖度最大值", example = "100000")
    private Integer coverageMax;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "生成时间开始", example = "2024-01-01 00:00:00")
    private LocalDateTime generateTimeStart;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "生成时间结束", example = "2024-01-31 23:59:59")
    private LocalDateTime generateTimeEnd;

    @Schema(description = "策略ID", example = "STRATEGY_001")
    private String strategyId;

    @Builder.Default
    @Schema(description = "是否只查询最新版本", example = "true")
    private Boolean latestOnly = true;

    @Max(value = 100000, message = "全量查询最大限制为100000条")
    @Builder.Default
    @Schema(description = "最大查询条数", example = "10000", maximum = "100000")
    private Integer maxLimit = 10000;
}
