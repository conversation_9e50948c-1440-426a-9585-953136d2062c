package com.iptv.flux.service.repository;

import com.iptv.flux.service.model.dto.UserGroupQueryAllRequestDTO;
import com.iptv.flux.service.model.dto.UserGroupQueryRequestDTO;
import com.iptv.flux.service.model.dto.UserGroupQueryResponseDTO;
import com.iptv.flux.common.dto.PagedResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jooq.*;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

import static org.jooq.impl.DSL.*;

/**
 * 用户分组查询Repository
 * 基于现有的 group_info 表实现查询功能
 */
@Repository
@Slf4j
@RequiredArgsConstructor
public class UserGroupQueryRepository {

    private final DSLContext dsl;

    // 表名常量
    private static final String GROUP_INFO_TABLE = "group_info";

    /**
     * 分页查询用户分组
     */
    public PagedResponseDTO<UserGroupQueryResponseDTO> queryWithPaging(UserGroupQueryRequestDTO request) {
        log.debug("流量平台-----> 开始分页查询用户分组: platform={}, page={}, pageSize={}",
                request.getPlatform(), request.getPage(), request.getPageSize());

        try {
            // 1. 构建查询条件
            Condition condition = buildQueryCondition(request);

            // 2. 查询总数
            long total = dsl.selectCount()
                    .from(table(GROUP_INFO_TABLE))
                    .where(condition)
                    .fetchOne(0, Long.class);

            // 3. 分页查询数据
            List<UserGroupQueryResponseDTO> data = dsl.select(
                        field("group_id"),
                        field("group_name"),
                        field("platform"),
                        field("coverage"),
                        field("file_url"),
                        field("generate_time"),
                        field("md5sum"),
                        field("description"),
                        field("created_at")
                    )
                    .from(table(GROUP_INFO_TABLE))
                    .where(condition)
                    .orderBy(field("generate_time").desc().nullsLast(), field("created_at").desc())
                    .limit(request.getPageSize())
                    .offset((request.getPage() - 1) * request.getPageSize())
                    .fetch(this::mapToResponseDTO);

            log.debug("流量平台-----> 分页查询完成，总数: {}, 返回: {} 条记录", total, data.size());

            return PagedResponseDTO.<UserGroupQueryResponseDTO>builder()
                    .list(data)
                    .total(total)
                    .page(request.getPage())
                    .pageSize(request.getPageSize())
                    .build();

        } catch (Exception e) {
            log.error("流量平台-----> 分页查询用户分组失败", e);
            throw new RuntimeException("分页查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 全量查询用户分组
     */
    public List<UserGroupQueryResponseDTO> queryAll(UserGroupQueryAllRequestDTO request) {
        log.debug("流量平台-----> 开始全量查询用户分组: platform={}, maxLimit={}",
                request.getPlatform(), request.getMaxLimit());

        try {
            // 构建查询条件
            Condition condition = buildQueryConditionForAll(request);

            List<UserGroupQueryResponseDTO> result = dsl.select(
                        field("group_id"),
                        field("group_name"),
                        field("platform"),
                        field("coverage"),
                        field("file_url"),
                        field("generate_time"),
                        field("md5sum"),
                        field("description"),
                        field("created_at")
                    )
                    .from(table(GROUP_INFO_TABLE))
                    .where(condition)
                    .orderBy(field("generate_time").desc().nullsLast(), field("created_at").desc())
                    .limit(request.getMaxLimit())
                    .fetch(this::mapToResponseDTO);

            log.debug("流量平台-----> 全量查询完成，返回: {} 条记录", result.size());
            return result;

        } catch (Exception e) {
            log.error("流量平台-----> 全量查询用户分组失败", e);
            throw new RuntimeException("全量查询失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量查询（用于流式导出）
     */
    public List<UserGroupQueryResponseDTO> queryBatch(UserGroupQueryAllRequestDTO request, int offset, int batchSize) {
        log.debug("流量平台-----> 开始批量查询用户分组: offset={}, batchSize={}", offset, batchSize);

        try {
            // 构建查询条件
            Condition condition = buildQueryConditionForAll(request);

            List<UserGroupQueryResponseDTO> result = dsl.select(
                        field("group_id"),
                        field("group_name"),
                        field("platform"),
                        field("coverage"),
                        field("file_url"),
                        field("generate_time"),
                        field("md5sum"),
                        field("description"),
                        field("created_at")
                    )
                    .from(table(GROUP_INFO_TABLE))
                    .where(condition)
                    .orderBy(field("generate_time").desc().nullsLast(), field("created_at").desc())
                    .limit(batchSize)
                    .offset(offset)
                    .fetch(this::mapToResponseDTO);

            log.debug("流量平台-----> 批量查询完成，返回: {} 条记录", result.size());
            return result;

        } catch (Exception e) {
            log.error("流量平台-----> 批量查询用户分组失败", e);
            throw new RuntimeException("批量查询失败: " + e.getMessage(), e);
        }
    }
    /**
     * 构建分页查询条件
     */
    private Condition buildQueryCondition(UserGroupQueryRequestDTO request) {
        Condition condition = trueCondition();

        // 平台筛选（必需）
        if (request.getPlatform() != null) {
            condition = condition.and(field("platform").eq(request.getPlatform()));
        }

        // 来源筛选（运营商代码）- 通过关联user_group_relation表实现
        if (request.getSource() != null) {
            // 使用EXISTS子查询，因为group_ids是逗号分隔的字符串
            condition = condition.and(exists(
                select(field("1"))
                .from(table("user_group_relation"))
                .where(field("source").eq(request.getSource()))
                .and(field("group_ids").like(concat(val("%"), field("group_info.group_id"), val("%"))))
            ));
        }

        // 分组ID筛选
        if (request.getGroupId() != null) {
            condition = condition.and(field("group_id").eq(request.getGroupId()));
        }

        // 分组名称模糊查询
        if (request.getGroupName() != null) {
            condition = condition.and(field("group_name").like("%" + request.getGroupName() + "%"));
        }

        // 覆盖度范围筛选
        if (request.getCoverageMin() != null) {
            condition = condition.and(field("coverage").ge(request.getCoverageMin()));
        }
        if (request.getCoverageMax() != null) {
            condition = condition.and(field("coverage").le(request.getCoverageMax()));
        }

        // 生成时间范围筛选
        if (request.getGenerateTimeStart() != null) {
            condition = condition.and(field("generate_time").ge(request.getGenerateTimeStart()));
        }
        if (request.getGenerateTimeEnd() != null) {
            condition = condition.and(field("generate_time").le(request.getGenerateTimeEnd()));
        }

        // 只查询激活的分组
        condition = condition.and(field("active").eq(1));

        return condition;
    }

    /**
     * 构建全量查询条件
     */
    private Condition buildQueryConditionForAll(UserGroupQueryAllRequestDTO request) {
        Condition condition = trueCondition();

        // 平台筛选（必需）
        if (request.getPlatform() != null) {
            condition = condition.and(field("platform").eq(request.getPlatform()));
        }

        // 来源筛选（运营商代码）- 通过关联user_group_relation表实现
        if (request.getSource() != null) {
            // 使用EXISTS子查询，因为group_ids是逗号分隔的字符串
            condition = condition.and(exists(
                select(field("1"))
                .from(table("user_group_relation"))
                .where(field("source").eq(request.getSource()))
                .and(field("group_ids").like(concat(val("%"), field("group_info.group_id"), val("%"))))
            ));
        }

        // 分组ID筛选
        if (request.getGroupId() != null) {
            condition = condition.and(field("group_id").eq(request.getGroupId()));
        }

        // 分组名称模糊查询
        if (request.getGroupName() != null) {
            condition = condition.and(field("group_name").like("%" + request.getGroupName() + "%"));
        }

        // 覆盖度范围筛选
        if (request.getCoverageMin() != null) {
            condition = condition.and(field("coverage").ge(request.getCoverageMin()));
        }
        if (request.getCoverageMax() != null) {
            condition = condition.and(field("coverage").le(request.getCoverageMax()));
        }

        // 生成时间范围筛选
        if (request.getGenerateTimeStart() != null) {
            condition = condition.and(field("generate_time").ge(request.getGenerateTimeStart()));
        }
        if (request.getGenerateTimeEnd() != null) {
            condition = condition.and(field("generate_time").le(request.getGenerateTimeEnd()));
        }

        // 只查询激活的分组
        condition = condition.and(field("active").eq(1));

        return condition;
    }

    /**
     * 映射查询结果到响应DTO
     */
    private UserGroupQueryResponseDTO mapToResponseDTO(org.jooq.Record record) {
        String groupId = record.get("group_id", String.class);

        // 查询该分组关联的source信息（取第一个匹配的source）
        String source = getSourceByGroupId(groupId);

        return UserGroupQueryResponseDTO.builder()
                .groupId(groupId)
                .groupName(record.get("group_name", String.class))
                .platform(record.get("platform", String.class))
                .source(source)
                .coverage(record.get("coverage", Long.class))
                .fileUrl(record.get("file_url", String.class))
                .generateTime(record.get("generate_time", java.time.LocalDateTime.class))
                .md5sum(record.get("md5sum", String.class))
                .description(record.get("description", String.class))
                .createdAt(record.get("created_at", java.time.LocalDateTime.class))
                .verified(true) // 基于现有表，默认为已验证
                .build();
    }

    /**
     * 根据groupId查询关联的source
     */
    private String getSourceByGroupId(String groupId) {
        try {
            org.jooq.Record record = dsl.select(field("source"))
                    .from(table("user_group_relation"))
                    .where(field("group_ids").like("%" + groupId + "%"))
                    .limit(1)
                    .fetchOne();

            return record != null ? record.get("source", String.class) : null;
        } catch (Exception e) {
            log.warn("流量平台-----> 查询分组{}的source失败", groupId, e);
            return null;
        }
    }
}