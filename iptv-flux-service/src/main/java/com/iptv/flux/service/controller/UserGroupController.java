package com.iptv.flux.service.controller;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.*;
import com.iptv.flux.common.swagger.SwaggerAnnotations;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.model.dto.UserGroupQueryAllRequestDTO;
import com.iptv.flux.service.model.dto.UserGroupQueryRequestDTO;
import com.iptv.flux.service.model.dto.UserGroupQueryResponseDTO;
import com.iptv.flux.service.service.BlacklistService;
import com.iptv.flux.service.service.GroupInfoService;
import com.iptv.flux.service.service.UserGroupService;
import com.iptv.flux.service.service.WhitelistService;
import com.iptv.flux.service.service.UserGroupQueryService;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.ratelimiter.annotation.RateLimiter;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.controller
 * @className: UserGroupController
 * @author: chiron
 * @description: 用户分组控制器
 * @date: 2025/2/28 12:58
 * @version: 1.0
 */
@RestController
@RequestMapping(UserGroupConstants.API_BASE_PATH)
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "用户分组管理", description = "用户分组查询与管理接口")
public class UserGroupController {

    private final UserGroupService userGroupService;
    private final GroupInfoService groupInfoService;
    private final BlacklistService blacklistService;
    private final WhitelistService whitelistService;
    private final MetricsRegistryUtil metricsRegistry;
    private final UserGroupQueryService userGroupQueryService;

    /**
     * 获取用户分组信息
     *
     * @param source 请求来源
     * @param userId 用户ID
     * @return 包含分组ID集合的结果
     */
    @GetMapping(value = "/{source}/{userId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @CircuitBreaker(name = UserGroupConstants.CIRCUIT_BREAKER_NAME, fallbackMethod = "getUserGroupFallback")
    @TimeLimiter(name = UserGroupConstants.CIRCUIT_BREAKER_NAME)
    @RateLimiter(name = UserGroupConstants.RATE_LIMITER_NAME)
    @Timed(value = "api.usergroup.get", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取用户分组信息",
            description = "根据来源和用户ID获取该用户所属的所有分组ID",
            responseType = Set.class
    )
    public CompletableFuture<ResultDTO<Set<String>>> getUserGroup(
            @PathVariable @NotBlank String source,
            @PathVariable @NotBlank String userId,
            HttpServletRequest request) {

        String clientIp = getClientIp(request);
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 收到获取用户分组请求。来源: {}, 用户ID: {}, 客户端IP: {}",
                source, userId, clientIp);

        metricsRegistry.incrementCounter(UserGroupConstants.QUERY_TOTAL_METRIC);

        return CompletableFuture.supplyAsync(() -> {
            try {
                Set<String> groups = userGroupService.loadUserGroupInfo(source, userId, clientIp);

                long executionTime = System.currentTimeMillis() - startTime;
                log.info("流量平台-----> 找到来源: {}, 用户ID: {}, 客户端IP: {} 的 {} 个分组，耗时 {}ms",
                        source, userId, clientIp, groups.size(), executionTime);

                return ResultDTO.success(groups);
            } catch (Exception e) {
                log.error("流量平台-----> 加载来源: {}, 用户ID: {}, 客户端IP: {} 的用户分组出错",
                        source, userId, clientIp, e);
                metricsRegistry.incrementCounter(UserGroupConstants.QUERY_ERROR_METRIC);
                throw e;
            }
        });
    }

    /**
     * 获取详细的用户分组信息，包含分组详情
     *
     * @param source 请求来源
     * @param userId 用户ID
     * @return 包含分组ID到分组信息映射的结果
     */
    @GetMapping(value = "/{source}/{userId}/detail", produces = MediaType.APPLICATION_JSON_VALUE)
    @CircuitBreaker(name = UserGroupConstants.CIRCUIT_BREAKER_NAME, fallbackMethod = "getUserGroupDetailFallback")
    @TimeLimiter(name = UserGroupConstants.CIRCUIT_BREAKER_NAME)
    @RateLimiter(name = UserGroupConstants.RATE_LIMITER_NAME)
    @Timed(value = "api.usergroup.detail.get", percentiles = {0.5, 0.95, 0.99})
    public CompletableFuture<ResultDTO<Map<String, GroupInfoDTO>>> getUserGroupDetail(
            @PathVariable @NotBlank String source,
            @PathVariable @NotBlank String userId,
            HttpServletRequest request) {

        String clientIp = getClientIp(request);
        long startTime = System.currentTimeMillis();
        log.debug("流量平台-----> 收到获取详细用户分组请求，来源: {}, 用户ID: {}, IP: {}",
                source, userId, clientIp);

        metricsRegistry.incrementCounter(UserGroupConstants.QUERY_TOTAL_METRIC, "detail", "true");

        return CompletableFuture.supplyAsync(() -> {
            try {
                Set<String> groupIds = userGroupService.loadUserGroupInfo(source, userId, clientIp);

                if (groupIds.isEmpty()) {
                    log.debug("流量平台-----> 来源: {}, 用户ID: {} 未找到分组", source, userId);
                    return ResultDTO.success(Collections.emptyMap());
                }

                Map<String, GroupInfoDTO> groupDetails = groupInfoService.getGroupInfoBatch(List.copyOf(groupIds));

                log.debug("流量平台-----> 来源: {}, 用户ID: {} 找到 {} 个分组详情，耗时 {}ms",
                        source, userId, groupDetails.size(), System.currentTimeMillis() - startTime);

                return ResultDTO.success(groupDetails);
            } catch (Exception e) {
                log.error("流量平台-----> 加载来源: {}, 用户ID: {} 的详细用户分组出错", source, userId, e);
                metricsRegistry.incrementCounter(UserGroupConstants.QUERY_ERROR_METRIC, "detail", "true");
                throw e;
            }
        });
    }

    /**
     * 保存用户分组信息
     *
     * @param userGroupDTO 用户分组数据
     * @return 成功响应
     */
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.save", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.SaveOperation(
            summary = "保存用户分组信息",
            description = "保存用户与分组的关联关系"
    )
    public ResultDTO<String> saveUserGroup(@RequestBody @Valid UserGroupDTO userGroupDTO) {
        log.info("流量平台-----> 正在保存用户ID: {}, 来源: {}, 分组数: {} 的用户分组",
                userGroupDTO.getUserId(), userGroupDTO.getSource(), userGroupDTO.getGroupIds().size());

        userGroupService.saveUserGroupInfo(userGroupDTO);
        return ResultDTO.success("操作成功");
    }

    /**
     * 保存分组信息
     *
     * @param groupInfoDTO 分组信息数据
     * @return 成功响应
     */
    @PostMapping(value = "/group", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.group.save", percentiles = {0.5, 0.95, 0.99})
    public ResultDTO<String> saveGroupInfo(@RequestBody @Valid GroupInfoDTO groupInfoDTO) {
        log.info("流量平台-----> 正在保存分组ID: {}, 业务ID: {} 的分组信息",
                groupInfoDTO.getGroupId(), groupInfoDTO.getBusinessId());

        groupInfoService.saveGroupInfo(groupInfoDTO);
        return ResultDTO.success("操作成功");
    }



    /**
     * getUserGroup的回退方法
     */
    public CompletableFuture<ResultDTO<Set<String>>> getUserGroupFallback(
            String source, String userId, Throwable ex) {
        log.error("流量平台-----> getUserGroup回退，来源: {}, 用户ID: {}", source, userId, ex);
        metricsRegistry.incrementCounter("fallback.usergroup");
        return CompletableFuture.completedFuture(ResultDTO.success(Collections.emptySet()));
    }

    /**
     * getUserGroupDetail的回退方法
     */
    public CompletableFuture<ResultDTO<Map<String, GroupInfoDTO>>> getUserGroupDetailFallback(
            String source, String userId, Throwable ex) {
        log.error("流量平台-----> getUserGroupDetail回退，来源: {}, 用户ID: {}", source, userId, ex);
        metricsRegistry.incrementCounter("fallback.usergroup.detail");
        return CompletableFuture.completedFuture(ResultDTO.success(Collections.emptyMap()));
    }

    // 添加获取客户端IP的辅助方法
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 如果是多级代理，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }

    /** 20250526 add controller **/

    /**
     * 获取用户分组列表（分页）
     */
    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.list", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取用户分组列表",
            description = "分页获取用户分组列表，可选择按来源和用户ID筛选",
            responseType = Map.class
    )
    public ResponseEntity<ResultDTO<Map<String, Object>>> getUserGroupList(
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize) {

        log.info("流量平台-----> 收到获取用户分组列表请求。来源: {}, 用户ID: {}, 页码: {}, 每页大小: {}",
                source, userId, page, pageSize);

        // 参数校验
        if (page < 1) {
            page = 1;
        }
        if (pageSize < 1 || pageSize > 100) {
            pageSize = 10;
        }

        try {
            Map<String, Object> result = userGroupService.getUserGroupList(source, userId, page, pageSize);
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ResultDTO.success(result));
        } catch (Exception e) {
            log.error("流量平台-----> 获取用户分组列表出错", e);
            metricsRegistry.incrementCounter(UserGroupConstants.QUERY_ERROR_METRIC);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ResultDTO.fail("获取用户分组列表失败：" + e.getMessage()));
        }
    }


    /**
     * 删除用户分组
     */
    @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.delete", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "删除用户分组",
            description = "根据ID删除用户分组关系",
            responseType = String.class
    )
    public ResponseEntity<ResultDTO<String>> deleteUserGroup(@PathVariable Long id) {
        log.info("流量平台-----> 收到删除用户分组请求。ID: {}", id);

        try {
            userGroupService.deleteUserGroup(id);
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ResultDTO.success("删除成功"));
        } catch (Exception e) {
            log.error("流量平台-----> 删除用户分组出错，ID: {}", id, e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ResultDTO.fail("删除用户分组失败：" + e.getMessage()));
        }
    }


    /**
     * 获取所有分组
     *
     * @return 分组列表
     */
    @GetMapping(value = "/groups", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.groups.getAll", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取所有分组",
            description = "获取系统中所有的分组信息",
            responseType = List.class
    )
    public ResultDTO<List<GroupInfoDTO>> getAllGroups() {
        log.info("流量平台-----> 收到获取所有分组请求");

        try {
            List<GroupInfoDTO> groups = groupInfoService.getAllGroups();
            return ResultDTO.success(groups);
        } catch (Exception e) {
            log.error("流量平台-----> 获取所有分组出错", e);
            return ResultDTO.fail("获取分组信息失败：" + e.getMessage());
        }
    }

    /**
     * 根据运营商获取分组
     *
     * @param source 运营商/来源
     * @return 分组列表
     */
    @GetMapping(value = "/groups/{source}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.groups.getBySource", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取指定运营商的分组",
            description = "根据运营商/来源获取相关的分组信息",
            responseType = List.class
    )
    public ResultDTO<List<GroupInfoDTO>> getGroupsBySource(@PathVariable String source) {
        log.info("流量平台-----> 收到获取运营商分组请求。来源: {}", source);

        try {
            List<GroupInfoDTO> groups = groupInfoService.getGroupsBySource(source);
            return ResultDTO.success(groups);
        } catch (Exception e) {
            log.error("流量平台-----> 获取运营商分组出错，来源: {}", source, e);
            return ResultDTO.fail("获取运营商分组失败：" + e.getMessage());
        }
    }

    /**
     * 根据条件查询分组信息
     *
     * @param request 查询条件
     * @return 分组列表
     */
    @PostMapping(value = "/groups/query", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.groups.queryByConditions", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "根据条件查询分组信息",
            description = "根据分组名称、业务ID、来源等条件查询分组信息，所有参数为空时返回全部分组",
            responseType = List.class
    )
    public ResultDTO<List<GroupInfoDTO>> queryGroups(@RequestBody GroupQueryRequestDTO request) {
        log.info("流量平台-----> 收到条件查询分组请求，分组名称: {}, 业务ID: {}, 来源: {}",
                request.getGroupName(), request.getBusinessId(), request.getSource());

        try {
            List<GroupInfoDTO> groups = groupInfoService.queryGroups(request);

            log.info("流量平台-----> 条件查询分组成功，结果数量: {}", groups.size());
            return ResultDTO.success(groups);

        } catch (Exception e) {
            log.error("流量平台-----> 条件查询分组失败", e);
            return ResultDTO.fail("查询分组信息失败：" + e.getMessage());
        }
    }

    // ==================== 黑名单管理接口 ====================

    /**
     * 获取黑名单列表
     */
    @PostMapping(value = "/blacklist", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "获取黑名单列表",
            description = "分页查询黑名单用户列表",
            responseType = PagedResponseDTO.class
    )
    public ResultDTO<PagedResponseDTO<BlacklistUserDTO>> getBlacklist(
            @Valid @RequestBody ListQueryRequestDTO request) {
        try {
            log.info("流量平台-----> 查询黑名单列表，页码: {}, 每页: {}", request.getPage(), request.getPageSize());

            PagedResponseDTO<BlacklistUserDTO> result = blacklistService.findPage(request);

            log.info("流量平台-----> 查询黑名单列表成功，总数: {}", result.getTotal());
            return ResultDTO.success(result);

        } catch (Exception e) {
            log.error("流量平台-----> 查询黑名单列表失败", e);
            return ResultDTO.fail("查询黑名单列表失败: " + e.getMessage());
        }
    }

    /**
     * 添加黑名单用户
     */
    @PostMapping(value = "/blacklist/add", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.SaveOperation(
            summary = "添加黑名单用户",
            description = "将用户添加到黑名单"
    )
    public ResultDTO<String> addBlacklist(@Valid @RequestBody BlacklistUserDTO dto) {
        try {
            log.info("流量平台-----> 添加黑名单用户: {}, 来源: {}", dto.getUserId(), dto.getSource());

            blacklistService.addUser(dto);

            log.info("流量平台-----> 添加黑名单用户成功: {}", dto.getUserId());
            return ResultDTO.success("添加黑名单用户成功");

        } catch (Exception e) {
            log.error("流量平台-----> 添加黑名单用户失败: {}", dto.getUserId(), e);
            return ResultDTO.fail("添加黑名单用户失败: " + e.getMessage());
        }
    }

    /**
     * 删除黑名单用户
     */
    @DeleteMapping(value = "/blacklist/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.SaveOperation(
            summary = "删除黑名单用户",
            description = "从黑名单中删除用户"
    )
    public ResultDTO<String> deleteBlacklist(@PathVariable("id") String id) {
        try {
            log.info("流量平台-----> 删除黑名单用户: {}", id);

            blacklistService.deleteUser(id);

            log.info("流量平台-----> 删除黑名单用户成功: {}", id);
            return ResultDTO.success("删除黑名单用户成功");

        } catch (Exception e) {
            log.error("流量平台-----> 删除黑名单用户失败: {}", id, e);
            return ResultDTO.fail("删除黑名单用户失败: " + e.getMessage());
        }
    }

    // ==================== 白名单管理接口 ====================

    /**
     * 获取白名单列表
     */
    @PostMapping(value = "/whitelist", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "获取白名单列表",
            description = "分页查询白名单用户列表",
            responseType = PagedResponseDTO.class
    )
    public ResultDTO<PagedResponseDTO<WhitelistUserDTO>> getWhitelist(
            @Valid @RequestBody ListQueryRequestDTO request) {
        try {
            log.info("流量平台-----> 查询白名单列表，页码: {}, 每页: {}", request.getPage(), request.getPageSize());

            PagedResponseDTO<WhitelistUserDTO> result = whitelistService.findPage(request);

            log.info("流量平台-----> 查询白名单列表成功，总数: {}", result.getTotal());
            return ResultDTO.success(result);

        } catch (Exception e) {
            log.error("流量平台-----> 查询白名单列表失败", e);
            return ResultDTO.fail("查询白名单列表失败: " + e.getMessage());
        }
    }

    /**
     * 添加白名单用户
     */
    @PostMapping(value = "/whitelist/add", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.SaveOperation(
            summary = "添加白名单用户",
            description = "将用户添加到白名单"
    )
    public ResultDTO<String> addWhitelist(@Valid @RequestBody WhitelistUserDTO dto) {
        try {
            log.info("流量平台-----> 添加白名单用户: {}, 来源: {}", dto.getUserId(), dto.getSource());

            whitelistService.addUser(dto);

            log.info("流量平台-----> 添加白名单用户成功: {}", dto.getUserId());
            return ResultDTO.success("添加白名单用户成功");

        } catch (Exception e) {
            log.error("流量平台-----> 添加白名单用户失败: {}", dto.getUserId(), e);
            return ResultDTO.fail("添加白名单用户失败: " + e.getMessage());
        }
    }

    /**
     * 删除白名单用户
     */
    @DeleteMapping(value = "/whitelist/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.SaveOperation(
            summary = "删除白名单用户",
            description = "从白名单中删除用户"
    )
    public ResultDTO<String> deleteWhitelist(@PathVariable("id") String id) {
        try {
            log.info("流量平台-----> 删除白名单用户: {}", id);

            whitelistService.deleteUser(id);

            log.info("流量平台-----> 删除白名单用户成功: {}", id);
            return ResultDTO.success("删除白名单用户成功");

        } catch (Exception e) {
            log.error("流量平台-----> 删除白名单用户失败: {}", id, e);
            return ResultDTO.fail("删除白名单用户失败: " + e.getMessage());
        }
    }

    /**
     * 健康检查端点 - 兼容性保留
     *
     * @return 状态消息
     */
    @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "用户分组服务健康检查",
            description = "检查用户分组服务的健康状态，建议使用 /api/monitor/health 获取完整系统状态",
            responseType = String.class
    )
    public ResultDTO<String> healthCheck() {
        log.debug("流量平台-----> 收到用户分组健康检查请求");
        return ResultDTO.success("用户分组服务运行正常");
    }

    // ==================== 新增：集约平台分组查询接口 ====================

    /**
     * 分页查询用户分组（集约平台）
     */
    @GetMapping(value = "/platform/page", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.platform.page", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "分页查询用户分组",
            description = "支持多维度条件的分页查询用户分组信息，用于集约平台数据展示",
            responseType = PagedResponseDTO.class
    )
    public ResultDTO<PagedResponseDTO<UserGroupQueryResponseDTO>> queryUserGroupsWithPaging(
            @RequestParam @NotBlank(message = "平台标识不能为空") String platform,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String groupId,
            @RequestParam(required = false) String groupName,
            @RequestParam(required = false) Integer coverageMin,
            @RequestParam(required = false) Integer coverageMax,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime generateTimeStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime generateTimeEnd,
            @RequestParam(required = false) String strategyId,
            @RequestParam(defaultValue = "true") Boolean latestOnly,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "50") Integer pageSize) {

        log.info("流量平台-----> 收到分页查询用户分组请求: platform={}, page={}, pageSize={}",
                platform, page, pageSize);

        try {
            UserGroupQueryRequestDTO request = UserGroupQueryRequestDTO.builder()
                    .platform(platform)
                    .source(source)
                    .groupId(groupId)
                    .groupName(groupName)
                    .coverageMin(coverageMin)
                    .coverageMax(coverageMax)
                    .generateTimeStart(generateTimeStart)
                    .generateTimeEnd(generateTimeEnd)
                    .strategyId(strategyId)
                    .latestOnly(latestOnly)
                    .page(page)
                    .pageSize(pageSize)
                    .build();

            PagedResponseDTO<UserGroupQueryResponseDTO> result = userGroupQueryService.queryUserGroupsWithPaging(request);

            log.info("流量平台-----> 分页查询完成，返回 {} 条记录，总数: {}",
                    result.getList().size(), result.getTotal());

            return ResultDTO.success(result);
        } catch (Exception e) {
            log.error("流量平台-----> 分页查询用户分组失败", e);
            return ResultDTO.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 全量查询用户分组（集约平台）
     */
    @GetMapping(value = "/platform/all", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.platform.all", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "全量查询用户分组",
            description = "支持条件筛选的全量查询用户分组信息，适用于数据导出等场景",
            responseType = List.class
    )
    public ResultDTO<List<UserGroupQueryResponseDTO>> queryAllUserGroups(
            @RequestParam @NotBlank(message = "平台标识不能为空") String platform,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String groupId,
            @RequestParam(required = false) String groupName,
            @RequestParam(required = false) Integer coverageMin,
            @RequestParam(required = false) Integer coverageMax,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime generateTimeStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime generateTimeEnd,
            @RequestParam(required = false) String strategyId,
            @RequestParam(defaultValue = "true") Boolean latestOnly,
            @RequestParam(defaultValue = "10000") Integer maxLimit) {

        log.info("流量平台-----> 收到全量查询用户分组请求: platform={}, maxLimit={}", platform, maxLimit);

        try {
            UserGroupQueryAllRequestDTO request = UserGroupQueryAllRequestDTO.builder()
                    .platform(platform)
                    .source(source)
                    .groupId(groupId)
                    .groupName(groupName)
                    .coverageMin(coverageMin)
                    .coverageMax(coverageMax)
                    .generateTimeStart(generateTimeStart)
                    .generateTimeEnd(generateTimeEnd)
                    .strategyId(strategyId)
                    .latestOnly(latestOnly)
                    .maxLimit(maxLimit)
                    .build();

            List<UserGroupQueryResponseDTO> result = userGroupQueryService.queryAllUserGroups(request);

            log.info("流量平台-----> 全量查询完成，返回 {} 条记录", result.size());

            return ResultDTO.success(result);
        } catch (Exception e) {
            log.error("流量平台-----> 全量查询用户分组失败", e);
            return ResultDTO.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 流式导出用户分组（集约平台）
     */
    @GetMapping(value = "/platform/export")
    @Timed(value = "api.usergroup.platform.export", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "流式导出用户分组",
            description = "支持大数据量的流式导出用户分组信息，返回JSON格式文件",
            responseType = String.class
    )
    public void exportUserGroups(
            @RequestParam @NotBlank(message = "平台标识不能为空") String platform,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String groupId,
            @RequestParam(required = false) String groupName,
            @RequestParam(required = false) Integer coverageMin,
            @RequestParam(required = false) Integer coverageMax,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime generateTimeStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime generateTimeEnd,
            @RequestParam(required = false) String strategyId,
            @RequestParam(defaultValue = "true") Boolean latestOnly,
            @RequestParam(defaultValue = "100000") Integer maxLimit,
            HttpServletResponse response) throws IOException {

        log.info("流量平台-----> 收到导出用户分组请求: platform={}", platform);

        response.setContentType("application/json;charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=user_groups_" +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".json");

        try (PrintWriter writer = response.getWriter()) {
            UserGroupQueryAllRequestDTO request = UserGroupQueryAllRequestDTO.builder()
                    .platform(platform)
                    .source(source)
                    .groupId(groupId)
                    .groupName(groupName)
                    .coverageMin(coverageMin)
                    .coverageMax(coverageMax)
                    .generateTimeStart(generateTimeStart)
                    .generateTimeEnd(generateTimeEnd)
                    .strategyId(strategyId)
                    .latestOnly(latestOnly)
                    .maxLimit(maxLimit)
                    .build();

            userGroupQueryService.exportUserGroups(request, writer);
            log.info("流量平台-----> 用户分组导出完成");
        } catch (Exception e) {
            log.error("流量平台-----> 导出用户分组失败", e);
            throw e;
        }
    }

}