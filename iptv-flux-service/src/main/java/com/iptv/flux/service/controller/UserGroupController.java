package com.iptv.flux.service.controller;

import com.iptv.flux.common.constants.UserGroupConstants;
import com.iptv.flux.common.dto.*;
import com.iptv.flux.common.swagger.SwaggerAnnotations;
import com.iptv.flux.common.utils.MetricsRegistryUtil;
import com.iptv.flux.service.model.dto.UserGroupQueryAllRequestDTO;
import com.iptv.flux.service.model.dto.UserGroupQueryRequestDTO;
import com.iptv.flux.service.model.dto.UserGroupQueryResponseDTO;
import com.iptv.flux.service.model.dto.UnifiedGroupQueryDTO;
import com.iptv.flux.service.model.dto.UnifiedUserGroupQueryDTO;
import com.iptv.flux.service.model.dto.UnifiedListManagementDTO;
import com.iptv.flux.service.service.BlacklistService;
import com.iptv.flux.service.service.GroupInfoService;
import com.iptv.flux.service.service.UserGroupService;
import com.iptv.flux.service.service.WhitelistService;
import com.iptv.flux.service.service.UserGroupQueryService;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.ratelimiter.annotation.RateLimiter;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import io.micrometer.core.annotation.Timed;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.io.PrintWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * @projectName: iptv-flux-manager
 * @package: com.iptv.flux.service.controller
 * @className: UserGroupController
 * @author: chiron
 * @description: 用户分组控制器
 * @date: 2025/2/28 12:58
 * @version: 1.0
 */
@RestController
@RequestMapping(UserGroupConstants.API_BASE_PATH)
@RequiredArgsConstructor
@Slf4j
@Validated
@Tag(name = "用户分组管理", description = "用户分组查询与管理接口")
public class UserGroupController {

    private final UserGroupService userGroupService;
    private final GroupInfoService groupInfoService;
    private final BlacklistService blacklistService;
    private final WhitelistService whitelistService;
    private final MetricsRegistryUtil metricsRegistry;
    private final UserGroupQueryService userGroupQueryService;

    /**
     * 统一的用户分组查询接口
     * 合并了原有的 /{source}/{userId} 和 /{source}/{userId}/detail 接口
     *
     * @param source 请求来源
     * @param userId 用户ID
     * @param includeDetail 是否包含详细信息
     * @param request HTTP请求
     * @return 根据includeDetail参数返回分组ID集合或详细信息映射
     */
    @GetMapping(value = "/{source}/{userId}", produces = MediaType.APPLICATION_JSON_VALUE)
    @CircuitBreaker(name = UserGroupConstants.CIRCUIT_BREAKER_NAME, fallbackMethod = "getUserGroupUnifiedFallback")
    @TimeLimiter(name = UserGroupConstants.CIRCUIT_BREAKER_NAME)
    @RateLimiter(name = UserGroupConstants.RATE_LIMITER_NAME)
    @Timed(value = "api.usergroup.unified.get", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取用户分组信息（统一接口）",
            description = "根据来源和用户ID获取用户分组信息。includeDetail=false时返回分组ID集合，includeDetail=true时返回详细信息映射",
            responseType = Object.class
    )
    public CompletableFuture<ResultDTO<Object>> getUserGroupUnified(
            @PathVariable @NotBlank String source,
            @PathVariable @NotBlank String userId,
            @RequestParam(defaultValue = "false") boolean includeDetail,
            HttpServletRequest request) {

        String clientIp = getClientIp(request);
        long startTime = System.currentTimeMillis();
        log.info("流量平台-----> 收到统一用户分组查询请求。来源: {}, 用户ID: {}, 包含详情: {}, 客户端IP: {}",
                source, userId, includeDetail, clientIp);

        metricsRegistry.incrementCounter(UserGroupConstants.QUERY_TOTAL_METRIC, "detail", String.valueOf(includeDetail));

        return CompletableFuture.supplyAsync(() -> {
            try {
                Set<String> groupIds = userGroupService.loadUserGroupInfo(source, userId, clientIp);

                if (groupIds.isEmpty()) {
                    log.debug("流量平台-----> 来源: {}, 用户ID: {} 未找到分组", source, userId);
                    return ResultDTO.success(includeDetail ? Collections.emptyMap() : Collections.emptySet());
                }

                Object result;
                if (includeDetail) {
                    // 返回详细信息映射
                    Map<String, GroupInfoDTO> groupDetails = groupInfoService.getGroupInfoBatch(List.copyOf(groupIds));
                    result = groupDetails;
                    log.debug("流量平台-----> 来源: {}, 用户ID: {} 找到 {} 个分组详情，耗时 {}ms",
                            source, userId, groupDetails.size(), System.currentTimeMillis() - startTime);
                } else {
                    // 返回分组ID集合
                    result = groupIds;
                    log.info("流量平台-----> 找到来源: {}, 用户ID: {}, 客户端IP: {} 的 {} 个分组，耗时 {}ms",
                            source, userId, clientIp, groupIds.size(), System.currentTimeMillis() - startTime);
                }

                return ResultDTO.success(result);
            } catch (Exception e) {
                log.error("流量平台-----> 加载来源: {}, 用户ID: {}, 客户端IP: {} 的用户分组出错",
                        source, userId, clientIp, e);
                metricsRegistry.incrementCounter(UserGroupConstants.QUERY_ERROR_METRIC, "detail", String.valueOf(includeDetail));
                throw e;
            }
        });
    }


    /**
     * 保存用户分组信息
     *
     * @param userGroupDTO 用户分组数据
     * @return 成功响应
     */
    @PostMapping(produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.save", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.SaveOperation(
            summary = "保存用户分组信息",
            description = "保存用户与分组的关联关系"
    )
    public ResultDTO<String> saveUserGroup(@RequestBody @Valid UserGroupDTO userGroupDTO) {
        log.info("流量平台-----> 正在保存用户ID: {}, 来源: {}, 分组数: {} 的用户分组",
                userGroupDTO.getUserId(), userGroupDTO.getSource(), userGroupDTO.getGroupIds().size());

        userGroupService.saveUserGroupInfo(userGroupDTO);
        return ResultDTO.success("操作成功");
    }

    /**
     * 保存分组信息
     *
     * @param groupInfoDTO 分组信息数据
     * @return 成功响应
     */
    @PostMapping(value = "/group", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.group.save", percentiles = {0.5, 0.95, 0.99})
    public ResultDTO<String> saveGroupInfo(@RequestBody @Valid GroupInfoDTO groupInfoDTO) {
        log.info("流量平台-----> 正在保存分组ID: {}, 业务ID: {} 的分组信息",
                groupInfoDTO.getGroupId(), groupInfoDTO.getBusinessId());

        groupInfoService.saveGroupInfo(groupInfoDTO);
        return ResultDTO.success("操作成功");
    }



    /**
     * 统一用户分组查询的回退方法
     */
    public CompletableFuture<ResultDTO<Object>> getUserGroupUnifiedFallback(
            String source, String userId, boolean includeDetail, HttpServletRequest request, Throwable ex) {
        log.error("流量平台-----> 统一用户分组查询回退，来源: {}, 用户ID: {}, 包含详情: {}",
                source, userId, includeDetail, ex);
        metricsRegistry.incrementCounter("fallback.usergroup.unified", "detail", String.valueOf(includeDetail));

        Object fallbackResult = includeDetail ? Collections.emptyMap() : Collections.emptySet();
        return CompletableFuture.completedFuture(ResultDTO.success(fallbackResult));
    }

    // 添加获取客户端IP的辅助方法
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("HTTP_X_FORWARDED_FOR");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }

        // 如果是多级代理，取第一个IP
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }

        return ip;
    }

    /** 20250526 add controller **/

    /**
     * 统一的分页查询接口
     * 合并了原有的 /list 和 /platform/page 接口
     */
    @PostMapping(value = "/query/page", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.unified.page", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "统一分页查询用户分组",
            description = "支持多维度条件的分页查询，兼容简单列表查询和复杂条件查询",
            responseType = Object.class
    )
    public ResultDTO<Object> queryUserGroupsPageUnified(
            @RequestBody(required = false) UnifiedUserGroupQueryDTO request) {

        // 如果请求为空，创建默认查询对象
        if (request == null) {
            request = UnifiedUserGroupQueryDTO.builder().build();
        }

        // 验证分页参数
        request.validatePagination();

        log.info("流量平台-----> 收到统一分页查询请求。平台: {}, 来源: {}, 用户ID: {}, 页码: {}, 每页大小: {}",
                request.getPlatform(), request.getSource(), request.getUserId(), request.getPage(), request.getPageSize());

        try {
            Object result;

            if (request.isSingleUserQuery()) {
                // 单用户查询，使用原有的简单分页逻辑
                Map<String, Object> simpleResult = userGroupService.getUserGroupList(
                        request.getSource(), request.getUserId(), request.getPage(), request.getPageSize());
                result = simpleResult;
                log.info("流量平台-----> 简单分页查询完成，用户ID: {}", request.getUserId());
            } else if (request.getPlatform() != null) {
                // 复杂条件查询，使用集约平台查询逻辑
                UserGroupQueryRequestDTO complexRequest = request.toPagedQueryDTO();
                PagedResponseDTO<UserGroupQueryResponseDTO> complexResult =
                        userGroupQueryService.queryUserGroupsWithPaging(complexRequest);
                result = complexResult;
                log.info("流量平台-----> 复杂分页查询完成，返回 {} 条记录，总数: {}",
                        complexResult.getList().size(), complexResult.getTotal());
            } else {
                // 默认使用简单查询
                Map<String, Object> defaultResult = userGroupService.getUserGroupList(
                        request.getSource(), request.getUserId(), request.getPage(), request.getPageSize());
                result = defaultResult;
                log.info("流量平台-----> 默认分页查询完成");
            }

            return ResultDTO.success(result);
        } catch (Exception e) {
            log.error("流量平台-----> 统一分页查询失败", e);
            metricsRegistry.incrementCounter(UserGroupConstants.QUERY_ERROR_METRIC);
            return ResultDTO.fail("分页查询失败：" + e.getMessage());
        }
    }

    /**
     * 向后兼容接口：获取用户分组列表（分页）
     *
     * @deprecated 请使用 POST /query/page 替代
     */
    @Deprecated
    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.list.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取用户分组列表（已废弃）",
            description = "已废弃，请使用 POST /query/page 替代",
            responseType = Map.class
    )
    public ResponseEntity<ResultDTO<Map<String, Object>>> getUserGroupList(
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String userId,
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int pageSize) {

        log.warn("流量平台-----> 使用了已废弃的接口 GET /list，建议使用 POST /query/page");

        // 转换为新的统一接口
        UnifiedUserGroupQueryDTO unifiedRequest = UnifiedUserGroupQueryDTO.builder()
                .source(source)
                .userId(userId)
                .page(page)
                .pageSize(pageSize)
                .build();

        ResultDTO<Object> result = queryUserGroupsPageUnified(unifiedRequest);

        if (result.isSuccess() && result.getData() instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> data = (Map<String, Object>) result.getData();
            return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(ResultDTO.success(data));
        } else {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ResultDTO.fail(result.getMessage()));
        }
    }


    /**
     * 删除用户分组
     */
    @DeleteMapping(value = "/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.delete", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "删除用户分组",
            description = "根据ID删除用户分组关系",
            responseType = String.class
    )
    public ResponseEntity<ResultDTO<String>> deleteUserGroup(@PathVariable Long id) {
        log.info("流量平台-----> 收到删除用户分组请求。ID: {}", id);

        try {
            userGroupService.deleteUserGroup(id);
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ResultDTO.success("删除成功"));
        } catch (Exception e) {
            log.error("流量平台-----> 删除用户分组出错，ID: {}", id, e);
            return ResponseEntity
                    .status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(ResultDTO.fail("删除用户分组失败：" + e.getMessage()));
        }
    }


    /**
     * 统一的分组查询接口
     * 合并了原有的 /groups、/groups/{source} 和 /groups/query 接口
     *
     * @param request 查询条件（可选）
     * @return 分组列表
     */
    @PostMapping(value = "/groups/query", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.groups.unified.query", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "统一分组查询接口",
            description = "根据条件查询分组信息。不传参数或所有参数为空时返回全部分组；只传source时按来源查询；传其他条件时进行条件查询",
            responseType = List.class
    )
    public ResultDTO<List<GroupInfoDTO>> queryGroupsUnified(
            @RequestBody(required = false) UnifiedGroupQueryDTO request) {

        // 如果请求为空，创建一个空的查询对象
        if (request == null) {
            request = UnifiedGroupQueryDTO.forAllGroups();
        }

        log.info("流量平台-----> 收到统一分组查询请求，分组名称: {}, 策略ID: {}, 业务ID: {}, 来源: {}",
                request.getGroupName(), request.getStrategyId(), request.getBusinessId(), request.getSource());

        try {
            // 转换为原有的DTO格式以保持向后兼容
            GroupQueryRequestDTO legacyRequest = request.toGroupQueryRequestDTO();
            List<GroupInfoDTO> groups = groupInfoService.queryGroups(legacyRequest);

            String queryType = determineQueryType(request);
            log.info("流量平台-----> 统一分组查询成功，查询类型: {}, 结果数量: {}", queryType, groups.size());
            return ResultDTO.success(groups);

        } catch (Exception e) {
            log.error("流量平台-----> 统一分组查询失败", e);
            return ResultDTO.fail("查询分组信息失败：" + e.getMessage());
        }
    }

    /**
     * 向后兼容接口：获取所有分组
     *
     * @deprecated 请使用 POST /groups/query 替代
     */
    @Deprecated
    @GetMapping(value = "/groups", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.groups.getAll.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取所有分组（已废弃）",
            description = "已废弃，请使用 POST /groups/query 替代",
            responseType = List.class
    )
    public ResultDTO<List<GroupInfoDTO>> getAllGroups() {
        log.warn("流量平台-----> 使用了已废弃的接口 GET /groups，建议使用 POST /groups/query");

        // 重定向到新的统一接口
        return queryGroupsUnified(UnifiedGroupQueryDTO.forAllGroups());
    }

    /**
     * 向后兼容接口：根据运营商获取分组
     *
     * @deprecated 请使用 POST /groups/query 替代
     */
    @Deprecated
    @GetMapping(value = "/groups/{source}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.groups.getBySource.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取指定运营商的分组（已废弃）",
            description = "已废弃，请使用 POST /groups/query 替代",
            responseType = List.class
    )
    public ResultDTO<List<GroupInfoDTO>> getGroupsBySource(@PathVariable String source) {
        log.warn("流量平台-----> 使用了已废弃的接口 GET /groups/{}，建议使用 POST /groups/query", source);

        // 重定向到新的统一接口
        return queryGroupsUnified(UnifiedGroupQueryDTO.forSource(source));
    }

    /**
     * 向后兼容接口：根据条件查询分组信息
     *
     * @deprecated 请使用统一的 POST /groups/query 接口
     */
    @Deprecated
    @PostMapping(value = "/groups/query/legacy", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.groups.queryByConditions.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "根据条件查询分组信息（已废弃）",
            description = "已废弃，请使用统一的 POST /groups/query 接口",
            responseType = List.class
    )
    public ResultDTO<List<GroupInfoDTO>> queryGroupsLegacy(@RequestBody GroupQueryRequestDTO request) {
        log.warn("流量平台-----> 使用了已废弃的接口 POST /groups/query/legacy，建议使用 POST /groups/query");

        // 转换为新的DTO格式
        UnifiedGroupQueryDTO unifiedRequest = UnifiedGroupQueryDTO.forConditions(
                request.getGroupName(), request.getStrategyId(), request.getSource());

        return queryGroupsUnified(unifiedRequest);
    }

    /**
     * 确定查询类型（用于日志记录）
     */
    private String determineQueryType(UnifiedGroupQueryDTO request) {
        if (request.isEmpty()) {
            return "全部分组";
        } else if (request.isSourceOnlyQuery()) {
            return "按来源查询";
        } else {
            return "条件查询";
        }
    }

    // ==================== 统一列表管理接口 ====================

    /**
     * 统一的列表查询接口
     * 合并了原有的黑名单和白名单查询接口
     */
    @PostMapping(value = "/list/{listType}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.list.unified.query", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "统一列表查询接口",
            description = "根据列表类型查询黑名单或白名单用户列表",
            responseType = PagedResponseDTO.class
    )
    public ResultDTO<PagedResponseDTO<?>> queryListUnified(
            @PathVariable String listType,
            @Valid @RequestBody ListQueryRequestDTO request) {

        try {
            UnifiedListManagementDTO unifiedRequest = UnifiedListManagementDTO.builder()
                    .listType(listType)
                    .userId(request.getUserId())
                    .source(request.getSource())
                    .page(request.getPage())
                    .pageSize(request.getPageSize())
                    .build();

            unifiedRequest.validateForQuery();

            log.info("流量平台-----> 统一列表查询，类型: {}, 页码: {}, 每页: {}",
                    listType, request.getPage(), request.getPageSize());

            if (unifiedRequest.isBlacklistOperation()) {
                PagedResponseDTO<BlacklistUserDTO> result = blacklistService.findPage(request);
                log.info("流量平台-----> 查询黑名单列表成功，总数: {}", result.getTotal());
                return ResultDTO.success(result);
            } else if (unifiedRequest.isWhitelistOperation()) {
                PagedResponseDTO<WhitelistUserDTO> result = whitelistService.findPage(request);
                log.info("流量平台-----> 查询白名单列表成功，总数: {}", result.getTotal());
                return ResultDTO.success(result);
            } else {
                return ResultDTO.fail("不支持的列表类型: " + listType);
            }

        } catch (Exception e) {
            log.error("流量平台-----> 统一列表查询失败，类型: {}", listType, e);
            return ResultDTO.fail("查询列表失败: " + e.getMessage());
        }
    }

    /**
     * 统一的列表添加接口
     * 合并了原有的黑名单和白名单添加接口
     */
    @PostMapping(value = "/list/{listType}/add", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.list.unified.add", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.SaveOperation(
            summary = "统一列表添加接口",
            description = "根据列表类型添加用户到黑名单或白名单"
    )
    public ResultDTO<String> addToListUnified(
            @PathVariable String listType,
            @Valid @RequestBody UnifiedListManagementDTO request) {

        try {
            request.setListType(listType);
            request.validateForAdd();

            log.info("流量平台-----> 统一列表添加，类型: {}, 用户ID: {}, 来源: {}",
                    listType, request.getUserId(), request.getSource());

            if (request.isBlacklistOperation()) {
                BlacklistUserDTO dto = request.toBlacklistUserDTO();
                blacklistService.addUser(dto);
                log.info("流量平台-----> 添加黑名单用户成功: {}", request.getUserId());
                return ResultDTO.success("添加黑名单用户成功");
            } else if (request.isWhitelistOperation()) {
                WhitelistUserDTO dto = request.toWhitelistUserDTO();
                whitelistService.addUser(dto);
                log.info("流量平台-----> 添加白名单用户成功: {}", request.getUserId());
                return ResultDTO.success("添加白名单用户成功");
            } else {
                return ResultDTO.fail("不支持的列表类型: " + listType);
            }

        } catch (Exception e) {
            log.error("流量平台-----> 统一列表添加失败，类型: {}, 用户ID: {}",
                    listType, request.getUserId(), e);
            return ResultDTO.fail("添加到列表失败: " + e.getMessage());
        }
    }

    /**
     * 统一的列表删除接口
     * 合并了原有的黑名单和白名单删除接口
     */
    @DeleteMapping(value = "/list/{listType}/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.list.unified.delete", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.SaveOperation(
            summary = "统一列表删除接口",
            description = "根据列表类型从黑名单或白名单中删除用户"
    )
    public ResultDTO<String> deleteFromListUnified(
            @PathVariable String listType,
            @PathVariable String id) {

        try {
            UnifiedListManagementDTO request = UnifiedListManagementDTO.forDelete(listType, id);
            request.validateForDelete();

            log.info("流量平台-----> 统一列表删除，类型: {}, ID: {}", listType, id);

            if (request.isBlacklistOperation()) {
                blacklistService.deleteUser(id);
                log.info("流量平台-----> 删除黑名单用户成功: {}", id);
                return ResultDTO.success("删除黑名单用户成功");
            } else if (request.isWhitelistOperation()) {
                whitelistService.deleteUser(id);
                log.info("流量平台-----> 删除白名单用户成功: {}", id);
                return ResultDTO.success("删除白名单用户成功");
            } else {
                return ResultDTO.fail("不支持的列表类型: " + listType);
            }

        } catch (Exception e) {
            log.error("流量平台-----> 统一列表删除失败，类型: {}, ID: {}", listType, id, e);
            return ResultDTO.fail("从列表删除失败: " + e.getMessage());
        }
    }

    // ==================== 向后兼容的黑白名单接口 ====================

    /**
     * 向后兼容接口：获取黑名单列表
     *
     * @deprecated 请使用 POST /list/blacklist 替代
     */
    @Deprecated
    @PostMapping(value = "/blacklist", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.blacklist.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取黑名单列表（已废弃）",
            description = "已废弃，请使用 POST /list/blacklist 替代",
            responseType = PagedResponseDTO.class
    )
    public ResultDTO<PagedResponseDTO<BlacklistUserDTO>> getBlacklist(
            @Valid @RequestBody ListQueryRequestDTO request) {
        log.warn("流量平台-----> 使用了已废弃的接口 POST /blacklist，建议使用 POST /list/blacklist");

        // 重定向到新的统一接口
        ResultDTO<PagedResponseDTO<?>> result = queryListUnified("blacklist", request);
        if (result.isSuccess()) {
            @SuppressWarnings("unchecked")
            PagedResponseDTO<BlacklistUserDTO> data = (PagedResponseDTO<BlacklistUserDTO>) result.getData();
            return ResultDTO.success(data);
        } else {
            return ResultDTO.fail(result.getMessage());
        }
    }

    /**
     * 向后兼容接口：添加黑名单用户
     *
     * @deprecated 请使用 POST /list/blacklist/add 替代
     */
    @Deprecated
    @PostMapping(value = "/blacklist/add", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.blacklist.add.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.SaveOperation(
            summary = "添加黑名单用户（已废弃）",
            description = "已废弃，请使用 POST /list/blacklist/add 替代"
    )
    public ResultDTO<String> addBlacklist(@Valid @RequestBody BlacklistUserDTO dto) {
        log.warn("流量平台-----> 使用了已废弃的接口 POST /blacklist/add，建议使用 POST /list/blacklist/add");

        // 转换为新的统一接口
        UnifiedListManagementDTO unifiedRequest = UnifiedListManagementDTO.forBlacklistAdd(
                dto.getUserId(), dto.getSource(), dto.getRemark());

        return addToListUnified("blacklist", unifiedRequest);
    }

    /**
     * 向后兼容接口：删除黑名单用户
     *
     * @deprecated 请使用 DELETE /list/blacklist/{id} 替代
     */
    @Deprecated
    @DeleteMapping(value = "/blacklist/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.blacklist.delete.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.SaveOperation(
            summary = "删除黑名单用户（已废弃）",
            description = "已废弃，请使用 DELETE /list/blacklist/{id} 替代"
    )
    public ResultDTO<String> deleteBlacklist(@PathVariable("id") String id) {
        log.warn("流量平台-----> 使用了已废弃的接口 DELETE /blacklist/{}，建议使用 DELETE /list/blacklist/{}", id, id);

        return deleteFromListUnified("blacklist", id);
    }

    /**
     * 向后兼容接口：获取白名单列表
     *
     * @deprecated 请使用 POST /list/whitelist 替代
     */
    @Deprecated
    @PostMapping(value = "/whitelist", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.whitelist.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "获取白名单列表（已废弃）",
            description = "已废弃，请使用 POST /list/whitelist 替代",
            responseType = PagedResponseDTO.class
    )
    public ResultDTO<PagedResponseDTO<WhitelistUserDTO>> getWhitelist(
            @Valid @RequestBody ListQueryRequestDTO request) {
        log.warn("流量平台-----> 使用了已废弃的接口 POST /whitelist，建议使用 POST /list/whitelist");

        // 重定向到新的统一接口
        ResultDTO<PagedResponseDTO<?>> result = queryListUnified("whitelist", request);
        if (result.isSuccess()) {
            @SuppressWarnings("unchecked")
            PagedResponseDTO<WhitelistUserDTO> data = (PagedResponseDTO<WhitelistUserDTO>) result.getData();
            return ResultDTO.success(data);
        } else {
            return ResultDTO.fail(result.getMessage());
        }
    }

    /**
     * 向后兼容接口：添加白名单用户
     *
     * @deprecated 请使用 POST /list/whitelist/add 替代
     */
    @Deprecated
    @PostMapping(value = "/whitelist/add", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.whitelist.add.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.SaveOperation(
            summary = "添加白名单用户（已废弃）",
            description = "已废弃，请使用 POST /list/whitelist/add 替代"
    )
    public ResultDTO<String> addWhitelist(@Valid @RequestBody WhitelistUserDTO dto) {
        log.warn("流量平台-----> 使用了已废弃的接口 POST /whitelist/add，建议使用 POST /list/whitelist/add");

        // 转换为新的统一接口
        UnifiedListManagementDTO unifiedRequest = UnifiedListManagementDTO.forWhitelistAdd(
                dto.getUserId(), dto.getSource(), dto.getRemark());

        return addToListUnified("whitelist", unifiedRequest);
    }

    /**
     * 向后兼容接口：删除白名单用户
     *
     * @deprecated 请使用 DELETE /list/whitelist/{id} 替代
     */
    @Deprecated
    @DeleteMapping(value = "/whitelist/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.whitelist.delete.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.SaveOperation(
            summary = "删除白名单用户（已废弃）",
            description = "已废弃，请使用 DELETE /list/whitelist/{id} 替代"
    )
    public ResultDTO<String> deleteWhitelist(@PathVariable("id") String id) {
        log.warn("流量平台-----> 使用了已废弃的接口 DELETE /whitelist/{}，建议使用 DELETE /list/whitelist/{}", id, id);

        return deleteFromListUnified("whitelist", id);
    }

    /**
     * 健康检查端点 - 兼容性保留
     *
     * @return 状态消息
     */
    @GetMapping(value = "/health", produces = MediaType.APPLICATION_JSON_VALUE)
    @SwaggerAnnotations.QueryOperation(
            summary = "用户分组服务健康检查",
            description = "检查用户分组服务的健康状态，建议使用 /api/monitor/health 获取完整系统状态",
            responseType = String.class
    )
    public ResultDTO<String> healthCheck() {
        log.debug("流量平台-----> 收到用户分组健康检查请求");
        return ResultDTO.success("用户分组服务运行正常");
    }

    // ==================== 新增：集约平台分组查询接口 ====================

    /**
     * 向后兼容接口：分页查询用户分组（集约平台）
     *
     * @deprecated 请使用 POST /query/page 替代
     */
    @Deprecated
    @GetMapping(value = "/platform/page", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.platform.page.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "分页查询用户分组（已废弃）",
            description = "已废弃，请使用 POST /query/page 替代",
            responseType = PagedResponseDTO.class
    )
    public ResultDTO<PagedResponseDTO<UserGroupQueryResponseDTO>> queryUserGroupsWithPaging(
            @RequestParam @NotBlank(message = "平台标识不能为空") String platform,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String groupId,
            @RequestParam(required = false) String groupName,
            @RequestParam(required = false) Integer coverageMin,
            @RequestParam(required = false) Integer coverageMax,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime generateTimeStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime generateTimeEnd,
            @RequestParam(required = false) String strategyId,
            @RequestParam(defaultValue = "true") Boolean latestOnly,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "50") Integer pageSize) {

        log.warn("流量平台-----> 使用了已废弃的接口 GET /platform/page，建议使用 POST /query/page");

        // 转换为新的统一接口
        UnifiedUserGroupQueryDTO unifiedRequest = UnifiedUserGroupQueryDTO.builder()
                .platform(platform)
                .source(source)
                .groupId(groupId)
                .groupName(groupName)
                .coverageMin(coverageMin)
                .coverageMax(coverageMax)
                .generateTimeStart(generateTimeStart)
                .generateTimeEnd(generateTimeEnd)
                .strategyId(strategyId)
                .latestOnly(latestOnly)
                .page(page)
                .pageSize(pageSize)
                .build();

        ResultDTO<Object> result = queryUserGroupsPageUnified(unifiedRequest);

        if (result.isSuccess() && result.getData() instanceof PagedResponseDTO) {
            @SuppressWarnings("unchecked")
            PagedResponseDTO<UserGroupQueryResponseDTO> data = (PagedResponseDTO<UserGroupQueryResponseDTO>) result.getData();
            return ResultDTO.success(data);
        } else {
            return ResultDTO.fail(result.getMessage());
        }
    }

    /**
     * 统一的全量查询和导出接口
     * 合并了原有的 /platform/all 和 /platform/export 接口
     */
    @PostMapping(value = "/query/data")
    @Timed(value = "api.usergroup.unified.data", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "统一全量查询和导出用户分组",
            description = "支持条件筛选的全量查询和导出。format=json时返回JSON数据，format=export时流式导出文件",
            responseType = Object.class
    )
    public void queryUserGroupsDataUnified(
            @RequestBody(required = false) UnifiedUserGroupQueryDTO request,
            HttpServletResponse response) throws IOException {

        // 如果请求为空，创建默认查询对象
        if (request == null) {
            request = UnifiedUserGroupQueryDTO.builder().build();
        }

        // 验证全量查询参数
        request.validateAllQuery();

        log.info("流量平台-----> 收到统一数据查询请求: platform={}, format={}, maxLimit={}",
                request.getPlatform(), request.getFormat(), request.getMaxLimit());

        try {
            if (request.isExportQuery()) {
                // 导出模式：流式导出
                response.setContentType("application/json;charset=UTF-8");
                response.setHeader("Content-Disposition", "attachment; filename=user_groups_" +
                        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".json");

                try (PrintWriter writer = response.getWriter()) {
                    UserGroupQueryAllRequestDTO exportRequest = request.toAllQueryDTO();
                    userGroupQueryService.exportUserGroups(exportRequest, writer);
                    log.info("流量平台-----> 用户分组导出完成");
                }
            } else {
                // JSON模式：返回JSON数据
                response.setContentType("application/json;charset=UTF-8");

                UserGroupQueryAllRequestDTO queryRequest = request.toAllQueryDTO();
                List<UserGroupQueryResponseDTO> result = userGroupQueryService.queryAllUserGroups(queryRequest);

                log.info("流量平台-----> 全量查询完成，返回 {} 条记录", result.size());

                // 直接写入响应
                try (PrintWriter writer = response.getWriter()) {
                    writer.write("{\"success\":true,\"data\":");
                    writer.write(com.iptv.flux.common.utils.JsonUtils.toJson(result));
                    writer.write(",\"total\":" + result.size() + "}");
                    writer.flush();
                }
            }
        } catch (Exception e) {
            log.error("流量平台-----> 统一数据查询失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType("application/json;charset=UTF-8");
            try (PrintWriter writer = response.getWriter()) {
                writer.write("{\"success\":false,\"message\":\"查询失败: " + e.getMessage() + "\"}");
                writer.flush();
            }
        }
    }

    /**
     * 向后兼容接口：全量查询用户分组（集约平台）
     *
     * @deprecated 请使用 POST /query/data 替代
     */
    @Deprecated
    @GetMapping(value = "/platform/all", produces = MediaType.APPLICATION_JSON_VALUE)
    @Timed(value = "api.usergroup.platform.all.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "全量查询用户分组（已废弃）",
            description = "已废弃，请使用 POST /query/data 替代",
            responseType = List.class
    )
    public ResultDTO<List<UserGroupQueryResponseDTO>> queryAllUserGroups(
            @RequestParam @NotBlank(message = "平台标识不能为空") String platform,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String groupId,
            @RequestParam(required = false) String groupName,
            @RequestParam(required = false) Integer coverageMin,
            @RequestParam(required = false) Integer coverageMax,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime generateTimeStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime generateTimeEnd,
            @RequestParam(required = false) String strategyId,
            @RequestParam(defaultValue = "true") Boolean latestOnly,
            @RequestParam(defaultValue = "10000") Integer maxLimit) {

        log.warn("流量平台-----> 使用了已废弃的接口 GET /platform/all，建议使用 POST /query/data");

        // 转换为新的统一接口
        UnifiedUserGroupQueryDTO unifiedRequest = UnifiedUserGroupQueryDTO.builder()
                .platform(platform)
                .source(source)
                .groupId(groupId)
                .groupName(groupName)
                .coverageMin(coverageMin)
                .coverageMax(coverageMax)
                .generateTimeStart(generateTimeStart)
                .generateTimeEnd(generateTimeEnd)
                .strategyId(strategyId)
                .latestOnly(latestOnly)
                .maxLimit(maxLimit)
                .format("json")
                .build();

        try {
            UserGroupQueryAllRequestDTO queryRequest = unifiedRequest.toAllQueryDTO();
            List<UserGroupQueryResponseDTO> result = userGroupQueryService.queryAllUserGroups(queryRequest);
            return ResultDTO.success(result);
        } catch (Exception e) {
            log.error("流量平台-----> 全量查询用户分组失败", e);
            return ResultDTO.fail("查询失败: " + e.getMessage());
        }
    }

    /**
     * 向后兼容接口：流式导出用户分组（集约平台）
     *
     * @deprecated 请使用 POST /query/data?format=export 替代
     */
    @Deprecated
    @GetMapping(value = "/platform/export")
    @Timed(value = "api.usergroup.platform.export.deprecated", percentiles = {0.5, 0.95, 0.99})
    @SwaggerAnnotations.QueryOperation(
            summary = "流式导出用户分组（已废弃）",
            description = "已废弃，请使用 POST /query/data?format=export 替代",
            responseType = String.class
    )
    public void exportUserGroups(
            @RequestParam @NotBlank(message = "平台标识不能为空") String platform,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String groupId,
            @RequestParam(required = false) String groupName,
            @RequestParam(required = false) Integer coverageMin,
            @RequestParam(required = false) Integer coverageMax,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime generateTimeStart,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime generateTimeEnd,
            @RequestParam(required = false) String strategyId,
            @RequestParam(defaultValue = "true") Boolean latestOnly,
            @RequestParam(defaultValue = "100000") Integer maxLimit,
            HttpServletResponse response) throws IOException {

        log.warn("流量平台-----> 使用了已废弃的接口 GET /platform/export，建议使用 POST /query/data");

        // 转换为新的统一接口
        UnifiedUserGroupQueryDTO unifiedRequest = UnifiedUserGroupQueryDTO.builder()
                .platform(platform)
                .source(source)
                .groupId(groupId)
                .groupName(groupName)
                .coverageMin(coverageMin)
                .coverageMax(coverageMax)
                .generateTimeStart(generateTimeStart)
                .generateTimeEnd(generateTimeEnd)
                .strategyId(strategyId)
                .latestOnly(latestOnly)
                .maxLimit(maxLimit)
                .format("export")
                .build();

        queryUserGroupsDataUnified(unifiedRequest, response);
    }

}