-- 数据库字段迁移脚本：businessId -> strategyId
-- 执行前请备份数据库！

-- 1. 为 group_info 表添加新的 strategy_id 字段
ALTER TABLE group_info ADD COLUMN strategy_id VARCHAR(255) COMMENT '策略ID';

-- 2. 将现有的 business_id 数据迁移到 strategy_id
UPDATE group_info SET strategy_id = business_id WHERE business_id IS NOT NULL;

-- 3. 为 strategy_id 字段添加索引（如果需要）
CREATE INDEX idx_group_info_strategy_id ON group_info(strategy_id);

-- 4. 可选：删除旧的 business_id 字段（谨慎操作，建议先在测试环境验证）
-- ALTER TABLE group_info DROP COLUMN business_id;

-- 验证迁移结果
SELECT 
    COUNT(*) as total_records,
    COUNT(strategy_id) as strategy_id_count,
    COUNT(business_id) as business_id_count
FROM group_info;

-- 检查是否有数据不一致的情况
SELECT * FROM group_info 
WHERE (strategy_id IS NULL AND business_id IS NOT NULL) 
   OR (strategy_id IS NOT NULL AND business_id IS NULL)
LIMIT 10;
