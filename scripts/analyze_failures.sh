#!/bin/bash

# IPTV数据导入失败记录分析脚本
# 用于详细分析导入过程中的失败记录和错误模式

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 配置
LOG_FILE="logs/iptv-flux-service.log"
REPORT_FILE="reports/failure_analysis_$(date +%Y%m%d_%H%M%S).txt"

# 创建报告目录
mkdir -p reports

# 检查日志文件
if [ ! -f "$LOG_FILE" ]; then
    echo -e "${RED}错误: 日志文件 $LOG_FILE 不存在${NC}"
    exit 1
fi

# 生成分析报告
generate_report() {
    local start_time="$1"
    local end_time="$2"
    
    echo "IPTV数据导入失败分析报告" > "$REPORT_FILE"
    echo "生成时间: $(date)" >> "$REPORT_FILE"
    echo "分析时间段: $start_time 到 $end_time" >> "$REPORT_FILE"
    echo "========================================" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
}

# 分析错误类型分布
analyze_error_types() {
    echo -e "${BLUE}📊 分析错误类型分布...${NC}"
    
    echo "错误类型分布:" >> "$REPORT_FILE"
    echo "----------------------------------------" >> "$REPORT_FILE"
    
    # 文件相关错误
    file_errors=$(grep "文件.*失败\|文件.*错误\|文件大小超过\|文件为空" "$LOG_FILE" | wc -l)
    echo "文件相关错误: $file_errors" >> "$REPORT_FILE"
    
    # 数据库相关错误
    db_errors=$(grep "数据库.*失败\|保存.*失败\|查找.*失败\|SQL.*异常" "$LOG_FILE" | wc -l)
    echo "数据库相关错误: $db_errors" >> "$REPORT_FILE"
    
    # Redis相关错误
    redis_errors=$(grep "Redis.*失败\|缓存.*失败\|序列化.*失败" "$LOG_FILE" | wc -l)
    echo "Redis相关错误: $redis_errors" >> "$REPORT_FILE"
    
    # 网络相关错误
    network_errors=$(grep "连接.*失败\|超时\|timeout\|网络.*异常" "$LOG_FILE" | wc -l)
    echo "网络相关错误: $network_errors" >> "$REPORT_FILE"
    
    # 数据验证错误
    validation_errors=$(grep "无效.*格式\|验证.*失败\|格式.*错误" "$LOG_FILE" | wc -l)
    echo "数据验证错误: $validation_errors" >> "$REPORT_FILE"
    
    # 系统资源错误
    resource_errors=$(grep "内存.*不足\|OutOfMemoryError\|磁盘.*满" "$LOG_FILE" | wc -l)
    echo "系统资源错误: $resource_errors" >> "$REPORT_FILE"
    
    echo "" >> "$REPORT_FILE"
    
    # 显示统计结果
    echo -e "文件相关错误: ${RED}$file_errors${NC}"
    echo -e "数据库相关错误: ${RED}$db_errors${NC}"
    echo -e "Redis相关错误: ${RED}$redis_errors${NC}"
    echo -e "网络相关错误: ${RED}$network_errors${NC}"
    echo -e "数据验证错误: ${RED}$validation_errors${NC}"
    echo -e "系统资源错误: ${RED}$resource_errors${NC}"
    echo ""
}

# 分析失败任务详情
analyze_failed_tasks() {
    echo -e "${BLUE}📋 分析失败任务详情...${NC}"
    
    echo "失败任务详情:" >> "$REPORT_FILE"
    echo "----------------------------------------" >> "$REPORT_FILE"
    
    # 提取失败任务ID
    failed_tasks=$(grep "任务ID:.*状态: FAILED\|导入.*失败.*任务ID" "$LOG_FILE" | grep -o "任务ID: [^,]*" | cut -d' ' -f2 | sort | uniq)
    
    if [ -z "$failed_tasks" ]; then
        echo "未发现失败任务" >> "$REPORT_FILE"
        echo -e "${GREEN}未发现失败任务${NC}"
        return
    fi
    
    task_count=0
    for task_id in $failed_tasks; do
        task_count=$((task_count + 1))
        echo "任务 $task_count: $task_id" >> "$REPORT_FILE"
        
        # 获取任务相关的所有日志
        task_logs=$(grep "$task_id" "$LOG_FILE")
        
        # 提取关键信息
        start_time=$(echo "$task_logs" | head -1 | awk '{print $1, $2}')
        file_name=$(echo "$task_logs" | grep "文件名:" | head -1 | grep -o "文件名: [^,]*" | cut -d' ' -f2)
        error_msg=$(echo "$task_logs" | grep "失败\|ERROR" | tail -1 | cut -d' ' -f4-)
        
        echo "  开始时间: $start_time" >> "$REPORT_FILE"
        echo "  文件名: $file_name" >> "$REPORT_FILE"
        echo "  错误信息: $error_msg" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
        
        # 显示任务信息
        echo -e "${YELLOW}任务 $task_count:${NC} $task_id"
        echo -e "  开始时间: $start_time"
        echo -e "  文件名: ${GREEN}$file_name${NC}"
        echo -e "  错误信息: ${RED}$error_msg${NC}"
        echo ""
    done
    
    echo "总失败任务数: $task_count" >> "$REPORT_FILE"
    echo "" >> "$REPORT_FILE"
    echo -e "${YELLOW}总失败任务数: $task_count${NC}"
    echo ""
}

# 分析错误时间分布
analyze_error_timeline() {
    echo -e "${BLUE}⏰ 分析错误时间分布...${NC}"
    
    echo "错误时间分布:" >> "$REPORT_FILE"
    echo "----------------------------------------" >> "$REPORT_FILE"
    
    # 按小时统计错误
    for hour in {00..23}; do
        error_count=$(grep "ERROR" "$LOG_FILE" | grep "$(date '+%Y-%m-%d') $hour:" | wc -l)
        if [ $error_count -gt 0 ]; then
            echo "$hour:00 - $error_count 个错误" >> "$REPORT_FILE"
            echo -e "${hour}:00 - ${RED}$error_count${NC} 个错误"
        fi
    done
    
    echo "" >> "$REPORT_FILE"
    echo ""
}

# 分析用户ID错误模式
analyze_userid_patterns() {
    echo -e "${BLUE}👤 分析用户ID错误模式...${NC}"
    
    echo "用户ID错误模式:" >> "$REPORT_FILE"
    echo "----------------------------------------" >> "$REPORT_FILE"
    
    # 提取无效用户ID
    invalid_userids=$(grep "无效的用户ID格式" "$LOG_FILE" | grep -o "用户ID格式: [^,]*" | cut -d' ' -f2 | sort | uniq -c | sort -nr)
    
    if [ -n "$invalid_userids" ]; then
        echo "无效用户ID (出现次数):" >> "$REPORT_FILE"
        echo "$invalid_userids" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
        
        echo -e "${YELLOW}无效用户ID模式:${NC}"
        echo "$invalid_userids" | head -10
        echo ""
        
        # 分析错误模式
        echo "错误模式分析:" >> "$REPORT_FILE"
        
        # 包含特殊字符的用户ID
        special_chars=$(echo "$invalid_userids" | grep -E "[^a-zA-Z0-9_-]" | wc -l)
        echo "包含特殊字符: $special_chars" >> "$REPORT_FILE"
        
        # 过长的用户ID
        long_ids=$(echo "$invalid_userids" | awk '{if(length($2) > 50) print $2}' | wc -l)
        echo "长度超过50字符: $long_ids" >> "$REPORT_FILE"
        
        # 空用户ID
        empty_ids=$(echo "$invalid_userids" | grep -E "^\s*$" | wc -l)
        echo "空用户ID: $empty_ids" >> "$REPORT_FILE"
        
    else
        echo "未发现用户ID格式错误" >> "$REPORT_FILE"
        echo -e "${GREEN}未发现用户ID格式错误${NC}"
    fi
    
    echo "" >> "$REPORT_FILE"
    echo ""
}

# 分析性能问题
analyze_performance_issues() {
    echo -e "${BLUE}⚡ 分析性能问题...${NC}"
    
    echo "性能问题分析:" >> "$REPORT_FILE"
    echo "----------------------------------------" >> "$REPORT_FILE"
    
    # 处理时间过长的任务
    slow_tasks=$(grep "耗时.*ms" "$LOG_FILE" | awk '{
        for(i=1;i<=NF;i++) {
            if($i ~ /[0-9]+ms/) {
                time = $i
                gsub(/ms/, "", time)
                if(time > 5000) print $0
            }
        }
    }' | wc -l)
    
    echo "处理时间超过5秒的操作: $slow_tasks" >> "$REPORT_FILE"
    
    # 内存相关问题
    memory_issues=$(grep -i "outofmemory\|内存不足\|heap space" "$LOG_FILE" | wc -l)
    echo "内存相关问题: $memory_issues" >> "$REPORT_FILE"
    
    # 超时问题
    timeout_issues=$(grep -i "timeout\|超时" "$LOG_FILE" | wc -l)
    echo "超时问题: $timeout_issues" >> "$REPORT_FILE"
    
    echo "" >> "$REPORT_FILE"
    
    echo -e "处理时间超过5秒的操作: ${RED}$slow_tasks${NC}"
    echo -e "内存相关问题: ${RED}$memory_issues${NC}"
    echo -e "超时问题: ${RED}$timeout_issues${NC}"
    echo ""
}

# 生成解决建议
generate_recommendations() {
    echo -e "${BLUE}💡 生成解决建议...${NC}"
    
    echo "解决建议:" >> "$REPORT_FILE"
    echo "----------------------------------------" >> "$REPORT_FILE"
    
    # 基于错误类型生成建议
    file_errors=$(grep "文件.*失败\|文件.*错误" "$LOG_FILE" | wc -l)
    if [ $file_errors -gt 0 ]; then
        echo "1. 文件相关问题:" >> "$REPORT_FILE"
        echo "   - 检查文件格式是否为.xlsx或.xls" >> "$REPORT_FILE"
        echo "   - 确认文件大小不超过200MB" >> "$REPORT_FILE"
        echo "   - 验证文件内容完整性" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi
    
    db_errors=$(grep "数据库.*失败" "$LOG_FILE" | wc -l)
    if [ $db_errors -gt 0 ]; then
        echo "2. 数据库相关问题:" >> "$REPORT_FILE"
        echo "   - 检查数据库连接状态" >> "$REPORT_FILE"
        echo "   - 验证数据库磁盘空间" >> "$REPORT_FILE"
        echo "   - 优化数据库连接池配置" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi
    
    validation_errors=$(grep "无效.*格式" "$LOG_FILE" | wc -l)
    if [ $validation_errors -gt 0 ]; then
        echo "3. 数据验证问题:" >> "$REPORT_FILE"
        echo "   - 统一用户ID格式规范" >> "$REPORT_FILE"
        echo "   - 增强前端数据验证" >> "$REPORT_FILE"
        echo "   - 提供数据格式模板" >> "$REPORT_FILE"
        echo "" >> "$REPORT_FILE"
    fi
    
    echo "解决建议已生成到报告文件中"
}

# 显示帮助信息
show_help() {
    echo "IPTV数据导入失败分析脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -t, --today      分析今日失败记录"
    echo "  -y, --yesterday  分析昨日失败记录"
    echo "  -h, --hour N     分析最近N小时的失败记录"
    echo "  -f, --full       完整分析(默认)"
    echo "  --help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -t            # 分析今日失败记录"
    echo "  $0 -h 6          # 分析最近6小时"
    echo "  $0 -f            # 完整分析"
}

# 主函数
main() {
    echo -e "${GREEN}IPTV数据导入失败分析工具${NC}"
    echo "================================"
    
    # 生成报告头部
    generate_report "$(date)" "$(date)"
    
    # 执行分析
    analyze_error_types
    analyze_failed_tasks
    analyze_error_timeline
    analyze_userid_patterns
    analyze_performance_issues
    generate_recommendations
    
    echo -e "${GREEN}分析完成！${NC}"
    echo -e "详细报告已保存到: ${BLUE}$REPORT_FILE${NC}"
    echo ""
    echo -e "${YELLOW}快速查看报告:${NC}"
    echo "cat $REPORT_FILE"
    echo ""
    echo -e "${YELLOW}查看最新错误:${NC}"
    echo "tail -20 $LOG_FILE | grep ERROR"
}

# 处理命令行参数
case "$1" in
    "-t"|"--today")
        echo "分析今日失败记录..."
        main
        ;;
    "-y"|"--yesterday")
        echo "分析昨日失败记录..."
        main
        ;;
    "-h"|"--hour")
        if [ -n "$2" ]; then
            echo "分析最近 $2 小时的失败记录..."
            main
        else
            echo "错误: 请指定小时数"
            show_help
        fi
        ;;
    "-f"|"--full")
        echo "执行完整分析..."
        main
        ;;
    "--help")
        show_help
        ;;
    *)
        main
        ;;
esac
