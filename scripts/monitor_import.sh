#!/bin/bash

# IPTV数据导入实时监控脚本
# 用于前后端联调过程中的问题追踪和分析

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
LOG_FILE="logs/iptv-flux-service.log"
MONITOR_INTERVAL=5
MAX_LINES=50

# 检查日志文件是否存在
if [ ! -f "$LOG_FILE" ]; then
    echo -e "${RED}错误: 日志文件 $LOG_FILE 不存在${NC}"
    echo "请确认应用已启动且日志路径正确"
    exit 1
fi

# 显示标题
show_header() {
    clear
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}   IPTV数据导入实时监控系统   ${NC}"
    echo -e "${BLUE}================================${NC}"
    echo -e "监控时间: ${GREEN}$(date '+%Y-%m-%d %H:%M:%S')${NC}"
    echo -e "日志文件: ${GREEN}$LOG_FILE${NC}"
    echo -e "刷新间隔: ${GREEN}${MONITOR_INTERVAL}秒${NC}"
    echo ""
}

# 统计错误信息
show_error_stats() {
    echo -e "${YELLOW}📊 错误统计 (最近1小时)${NC}"
    echo "----------------------------------------"
    
    # 获取最近1小时的时间戳
    one_hour_ago=$(date -d '1 hour ago' '+%Y-%m-%d %H:%M:%S')
    
    # 统计各类错误
    total_errors=$(grep "ERROR" "$LOG_FILE" | grep "$(date '+%Y-%m-%d')" | wc -l)
    upload_errors=$(grep "文件上传处理失败" "$LOG_FILE" | grep "$(date '+%Y-%m-%d')" | wc -l)
    db_errors=$(grep "数据库.*失败" "$LOG_FILE" | grep "$(date '+%Y-%m-%d')" | wc -l)
    redis_errors=$(grep "Redis.*失败" "$LOG_FILE" | grep "$(date '+%Y-%m-%d')" | wc -l)
    batch_errors=$(grep "批次.*处理失败" "$LOG_FILE" | grep "$(date '+%Y-%m-%d')" | wc -l)
    
    echo -e "总错误数: ${RED}$total_errors${NC}"
    echo -e "文件上传错误: ${RED}$upload_errors${NC}"
    echo -e "数据库错误: ${RED}$db_errors${NC}"
    echo -e "Redis错误: ${RED}$redis_errors${NC}"
    echo -e "批次处理错误: ${RED}$batch_errors${NC}"
    echo ""
}

# 显示最新错误
show_recent_errors() {
    echo -e "${RED}🚨 最新错误 (最近10条)${NC}"
    echo "----------------------------------------"
    
    tail -1000 "$LOG_FILE" | grep "ERROR" | tail -10 | while read line; do
        timestamp=$(echo "$line" | awk '{print $1, $2}')
        error_msg=$(echo "$line" | cut -d' ' -f4-)
        echo -e "${YELLOW}[$timestamp]${NC} $error_msg"
    done
    echo ""
}

# 显示导入任务状态
show_import_status() {
    echo -e "${GREEN}📈 导入任务状态${NC}"
    echo "----------------------------------------"
    
    # 获取最新的任务信息
    recent_tasks=$(tail -200 "$LOG_FILE" | grep "任务ID:" | tail -5)
    
    if [ -z "$recent_tasks" ]; then
        echo "暂无活跃的导入任务"
    else
        echo "$recent_tasks" | while read line; do
            task_id=$(echo "$line" | grep -o "任务ID: [^,]*" | cut -d' ' -f2)
            status=$(echo "$line" | grep -o "状态: [^,]*" | cut -d' ' -f2)
            timestamp=$(echo "$line" | awk '{print $1, $2}')
            
            case "$status" in
                "PROCESSING")
                    echo -e "${YELLOW}[$timestamp]${NC} 任务 $task_id: ${YELLOW}处理中${NC}"
                    ;;
                "COMPLETED")
                    echo -e "${YELLOW}[$timestamp]${NC} 任务 $task_id: ${GREEN}已完成${NC}"
                    ;;
                "FAILED")
                    echo -e "${YELLOW}[$timestamp]${NC} 任务 $task_id: ${RED}失败${NC}"
                    ;;
                *)
                    echo -e "${YELLOW}[$timestamp]${NC} 任务 $task_id: $status"
                    ;;
            esac
        done
    fi
    echo ""
}

# 显示进度更新
show_progress_updates() {
    echo -e "${BLUE}📊 进度更新 (最近5条)${NC}"
    echo "----------------------------------------"
    
    tail -200 "$LOG_FILE" | grep "进度已更新" | tail -5 | while read line; do
        timestamp=$(echo "$line" | awk '{print $1, $2}')
        task_id=$(echo "$line" | grep -o "任务ID: [^,]*" | cut -d' ' -f2)
        progress=$(echo "$line" | grep -o "进度: [0-9.]*%" | cut -d' ' -f2)
        echo -e "${YELLOW}[$timestamp]${NC} 任务 $task_id: 进度 ${GREEN}$progress${NC}"
    done
    echo ""
}

# 显示系统性能
show_system_performance() {
    echo -e "${BLUE}💻 系统性能${NC}"
    echo "----------------------------------------"
    
    # CPU使用率
    cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    echo -e "CPU使用率: ${GREEN}${cpu_usage}%${NC}"
    
    # 内存使用率
    mem_info=$(free | grep Mem)
    mem_total=$(echo $mem_info | awk '{print $2}')
    mem_used=$(echo $mem_info | awk '{print $3}')
    mem_usage=$(echo "scale=1; $mem_used * 100 / $mem_total" | bc)
    echo -e "内存使用率: ${GREEN}${mem_usage}%${NC}"
    
    # 磁盘使用率
    disk_usage=$(df -h / | awk 'NR==2 {print $5}')
    echo -e "磁盘使用率: ${GREEN}$disk_usage${NC}"
    
    # Java进程状态
    java_pid=$(pgrep -f "iptv-flux-service")
    if [ -n "$java_pid" ]; then
        echo -e "Java进程: ${GREEN}运行中 (PID: $java_pid)${NC}"
    else
        echo -e "Java进程: ${RED}未运行${NC}"
    fi
    echo ""
}

# 显示网络连接状态
show_network_status() {
    echo -e "${BLUE}🌐 网络连接状态${NC}"
    echo "----------------------------------------"
    
    # MySQL连接
    mysql_conn=$(netstat -an | grep :3306 | grep ESTABLISHED | wc -l)
    echo -e "MySQL连接数: ${GREEN}$mysql_conn${NC}"
    
    # Redis连接
    redis_conn=$(netstat -an | grep :6379 | grep ESTABLISHED | wc -l)
    echo -e "Redis连接数: ${GREEN}$redis_conn${NC}"
    
    # HTTP连接
    http_conn=$(netstat -an | grep :7003 | grep ESTABLISHED | wc -l)
    echo -e "HTTP连接数: ${GREEN}$http_conn${NC}"
    echo ""
}

# 显示快捷操作提示
show_quick_actions() {
    echo -e "${YELLOW}🔧 快捷操作${NC}"
    echo "----------------------------------------"
    echo "Ctrl+C: 退出监控"
    echo "查看完整日志: tail -f $LOG_FILE"
    echo "查看错误日志: tail -f $LOG_FILE | grep ERROR"
    echo "查看特定任务: grep '任务ID: YOUR_TASK_ID' $LOG_FILE"
    echo ""
}

# 主监控循环
main_monitor() {
    while true; do
        show_header
        show_error_stats
        show_recent_errors
        show_import_status
        show_progress_updates
        show_system_performance
        show_network_status
        show_quick_actions
        
        echo -e "${BLUE}下次刷新: ${MONITOR_INTERVAL}秒后...${NC}"
        sleep $MONITOR_INTERVAL
    done
}

# 处理命令行参数
case "$1" in
    "errors")
        echo -e "${RED}🚨 错误日志监控${NC}"
        tail -f "$LOG_FILE" | grep --color=always "ERROR"
        ;;
    "progress")
        echo -e "${GREEN}📈 进度监控${NC}"
        tail -f "$LOG_FILE" | grep --color=always "进度已更新"
        ;;
    "tasks")
        echo -e "${BLUE}📋 任务监控${NC}"
        tail -f "$LOG_FILE" | grep --color=always "任务ID:"
        ;;
    "stats")
        show_header
        show_error_stats
        show_system_performance
        show_network_status
        ;;
    "help"|"-h"|"--help")
        echo "IPTV数据导入监控脚本"
        echo ""
        echo "用法: $0 [选项]"
        echo ""
        echo "选项:"
        echo "  无参数    启动完整监控界面"
        echo "  errors    只监控错误日志"
        echo "  progress  只监控进度更新"
        echo "  tasks     只监控任务状态"
        echo "  stats     显示统计信息"
        echo "  help      显示此帮助信息"
        echo ""
        echo "示例:"
        echo "  $0              # 启动完整监控"
        echo "  $0 errors       # 只监控错误"
        echo "  $0 progress     # 只监控进度"
        ;;
    *)
        # 默认启动完整监控
        echo -e "${GREEN}启动IPTV数据导入实时监控...${NC}"
        echo "按 Ctrl+C 退出监控"
        sleep 2
        main_monitor
        ;;
esac
