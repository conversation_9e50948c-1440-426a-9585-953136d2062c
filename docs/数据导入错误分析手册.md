# 🔧 数据导入错误分析手册

## 📋 错误分类与解决方案

### 🚨 Level 1: 文件级别错误

#### **错误1: 文件大小超限**
```log
流量平台-----> 文件大小超过限制: 200MB，实际大小: XXXmb
```
**原因分析**: 上传文件超过系统设定的200MB限制
**解决方案**:
1. 将大文件拆分为多个小文件
2. 压缩Excel文件减小体积
3. 联系管理员调整文件大小限制

#### **错误2: 文件格式不支持**
```log
不支持的文件格式，请上传.xlsx或.xls文件
```
**原因分析**: 上传的文件不是Excel格式
**解决方案**:
1. 确保文件扩展名为.xlsx或.xls
2. 使用Excel重新保存文件
3. 检查文件是否损坏

#### **错误3: 文件为空**
```log
流量平台-----> 上传的文件为空
```
**原因分析**: 上传的文件没有内容或文件损坏
**解决方案**:
1. 检查文件是否包含数据
2. 重新生成Excel文件
3. 确认文件上传过程中没有中断

### 🚨 Level 2: 数据级别错误

#### **错误4: 用户ID格式无效**
```log
流量平台-----> 无效的用户ID格式: XXX
流量平台-----> 处理用户ID XXX 的分组关系失败
```
**原因分析**: Excel中的用户ID格式不符合系统要求
**解决方案**:
1. 检查用户ID是否包含特殊字符
2. 确保用户ID不为空
3. 验证用户ID长度是否合理
4. 统一用户ID格式(建议纯数字或字母数字组合)

**数据验证规则**:
```javascript
// 推荐的用户ID格式
const validUserIdPattern = /^[a-zA-Z0-9_-]{1,50}$/;
```

#### **错误5: Excel数据结构错误**
```log
流量平台-----> Excel文件预览失败
流量平台-----> 获取Excel文件信息失败
```
**原因分析**: Excel文件内部结构不符合预期
**解决方案**:
1. 确保Excel第一列为用户ID
2. 检查是否有合并单元格
3. 验证数据行是否从第2行开始
4. 确认没有隐藏的特殊字符

**标准Excel格式**:
```
| 用户ID    | 其他列(可选) |
|-----------|-------------|
| user001   | ...         |
| user002   | ...         |
```

### 🚨 Level 3: 系统级别错误

#### **错误6: 数据库连接失败**
```log
流量平台-----> 保存用户ID: XXX, 来源: XXX 的用户分组关系失败
流量平台-----> 查找用户ID: XXX, 来源: XXX 的用户分组关系失败
```
**原因分析**: 数据库连接异常或事务失败
**解决方案**:
1. 检查数据库服务状态
2. 验证数据库连接池配置
3. 确认数据库磁盘空间充足
4. 检查数据库锁等待情况

**检查命令**:
```sql
-- 检查数据库连接
SHOW PROCESSLIST;

-- 检查表锁状态
SHOW OPEN TABLES WHERE In_use > 0;

-- 检查磁盘空间
SELECT table_schema, ROUND(SUM(data_length + index_length) / 1024 / 1024, 1) AS 'DB Size in MB' 
FROM information_schema.tables GROUP BY table_schema;
```

#### **错误7: Redis缓存失败**
```log
流量平台-----> 序列化进度失败，任务ID: XXX
流量平台-----> 保存进度到Redis失败，任务ID: XXX
```
**原因分析**: Redis连接异常或内存不足
**解决方案**:
1. 检查Redis服务状态
2. 验证Redis内存使用情况
3. 确认Redis连接配置正确
4. 检查网络连接稳定性

**Redis检查命令**:
```bash
# 检查Redis状态
redis-cli ping

# 检查内存使用
redis-cli info memory

# 检查连接数
redis-cli info clients
```

#### **错误8: 分布式锁获取失败**
```log
流量平台-----> 无法获取导入锁，任务ID: XXX
流量平台-----> 无法获取处理锁，请稍后重试
```
**原因分析**: 并发导入任务过多或锁超时
**解决方案**:
1. 等待当前任务完成后重试
2. 检查是否有僵死锁
3. 调整锁超时时间
4. 限制并发导入任务数量

### 🚨 Level 4: 性能级别错误

#### **错误9: 内存溢出**
```log
java.lang.OutOfMemoryError: Java heap space
流量平台-----> 批次处理失败
```
**原因分析**: 处理大文件时内存不足
**解决方案**:
1. 增加JVM堆内存大小
2. 减小批次处理大小
3. 使用流式处理模式
4. 分批上传大文件

**JVM参数调优**:
```bash
-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200
```

#### **错误10: 处理超时**
```log
流量平台-----> 文件导入失败：timeout
流量平台-----> 处理异常: 超时
```
**原因分析**: 文件过大或系统负载过高
**解决方案**:
1. 使用异步处理模式
2. 增加超时时间配置
3. 优化数据库查询性能
4. 分批处理大文件

## 🔍 错误诊断流程

### **步骤1: 收集错误信息**
```bash
# 获取完整错误堆栈
grep -A 10 -B 5 "ERROR" logs/iptv-flux-service.log

# 获取任务相关日志
grep "任务ID: XXX" logs/iptv-flux-service.log
```

### **步骤2: 分析错误类型**
1. **文件问题**: 检查文件格式、大小、内容
2. **数据问题**: 验证数据格式、完整性
3. **系统问题**: 检查数据库、Redis、网络
4. **性能问题**: 监控内存、CPU、磁盘IO

### **步骤3: 定位根本原因**
```bash
# 检查系统资源
top -p $(pgrep java)
free -h
df -h

# 检查网络连接
netstat -an | grep :3306  # MySQL
netstat -an | grep :6379  # Redis
```

### **步骤4: 实施解决方案**
1. 根据错误类型选择对应解决方案
2. 验证修复效果
3. 记录解决过程
4. 更新监控规则

## 📊 错误预防措施

### **前端预防**
1. 文件上传前验证格式和大小
2. 提供文件格式模板下载
3. 实现断点续传功能
4. 添加上传进度显示

### **后端预防**
1. 增强数据验证逻辑
2. 实现优雅降级机制
3. 添加熔断器保护
4. 优化批处理策略

### **运维预防**
1. 监控系统资源使用
2. 定期清理日志文件
3. 备份重要配置
4. 建立告警机制

## 🚀 性能优化建议

### **数据库优化**
```sql
-- 添加索引
CREATE INDEX idx_user_source ON user_group_relation(user_id, source);

-- 优化批量插入
SET autocommit = 0;
-- 批量操作
COMMIT;
```

### **应用优化**
```java
// 调整批次大小
@Value("${data-import.batch-size:1000}")
private int batchSize;

// 使用连接池
@Bean
public HikariDataSource dataSource() {
    HikariConfig config = new HikariConfig();
    config.setMaximumPoolSize(20);
    config.setMinimumIdle(5);
    return new HikariDataSource(config);
}
```

### **系统优化**
```bash
# 调整文件描述符限制
ulimit -n 65536

# 优化TCP参数
echo 'net.core.somaxconn = 65535' >> /etc/sysctl.conf
```

---

## 📞 紧急联系流程

### **Level 1: 一般错误**
- 查阅本手册解决
- 记录解决过程

### **Level 2: 系统错误**
- 联系运维团队
- 检查系统状态

### **Level 3: 紧急故障**
- 立即联系技术负责人
- 启动应急预案
- 通知相关业务方
