# UserGroupController 测试用例更新指南

## 📋 测试策略概述

本次 API 重构需要更新相关的测试用例，确保新接口的功能正确性和向后兼容性。

## 🧪 需要更新的测试类

### 1. UserGroupControllerTest

#### 新增测试方法

```java
@Test
public void testGetUserGroupUnified_WithoutDetail() {
    // 测试统一接口返回分组ID集合
}

@Test
public void testGetUserGroupUnified_WithDetail() {
    // 测试统一接口返回详细信息
}

@Test
public void testGetUserGroupDetail_Deprecated() {
    // 测试废弃接口的向后兼容性
}

@Test
public void testQueryGroupsUnified_AllGroups() {
    // 测试查询所有分组
}

@Test
public void testQueryGroupsUnified_BySource() {
    // 测试按来源查询分组
}

@Test
public void testQueryGroupsUnified_WithConditions() {
    // 测试条件查询分组
}

@Test
public void testQueryUserGroupsPageUnified_SimpleQuery() {
    // 测试简单分页查询
}

@Test
public void testQueryUserGroupsPageUnified_ComplexQuery() {
    // 测试复杂分页查询
}

@Test
public void testQueryUserGroupsDataUnified_JsonFormat() {
    // 测试JSON格式的全量查询
}

@Test
public void testQueryUserGroupsDataUnified_ExportFormat() {
    // 测试导出格式的全量查询
}

@Test
public void testQueryListUnified_Blacklist() {
    // 测试统一黑名单查询
}

@Test
public void testQueryListUnified_Whitelist() {
    // 测试统一白名单查询
}

@Test
public void testAddToListUnified_Blacklist() {
    // 测试统一黑名单添加
}

@Test
public void testDeleteFromListUnified_Blacklist() {
    // 测试统一黑名单删除
}
```

#### 需要修改的现有测试

```java
@Test
public void testGetUserGroup() {
    // 更新为测试新的统一接口
    // 验证 includeDetail=false 的行为
}

@Test
public void testGetAllGroups() {
    // 标记为测试废弃接口
    // 验证向后兼容性
}

@Test
public void testGetGroupsBySource() {
    // 标记为测试废弃接口
    // 验证重定向到新接口
}
```

### 2. DTO 测试类

#### GroupInfoDTOTest

```java
@Test
public void testStrategyIdField() {
    // 测试 strategyId 字段
    GroupInfoDTO dto = GroupInfoDTO.builder()
            .groupId("GROUP_001")
            .strategyId("STRATEGY_001")
            .build();

    assertEquals("STRATEGY_001", dto.getStrategyId());
    assertTrue(dto.hasStrategyId());
}

@Test
public void testStrategyIdValidation() {
    // 测试策略ID验证
    GroupInfoDTO dto = GroupInfoDTO.builder()
            .groupId("GROUP_001")
            .strategyId("STRATEGY_001")
            .build();

    assertEquals("STRATEGY_001", dto.getTrimmedStrategyId());
}

@Test
public void testWithStrategyIdFactory() {
    // 测试新的工厂方法
    GroupInfoDTO dto = GroupInfoDTO.withStrategyId(
            "GROUP_001", "STRATEGY_001", "测试分组", "测试描述");
    
    assertEquals("STRATEGY_001", dto.getStrategyId());
    assertEquals("STRATEGY_001", dto.getBusinessId()); // 向后兼容
}
```

#### UnifiedUserGroupQueryDTOTest

```java
@Test
public void testValidation() {
    // 测试参数验证
    UnifiedUserGroupQueryDTO dto = new UnifiedUserGroupQueryDTO();
    dto.setPage(0); // 无效页码
    
    assertThrows(IllegalArgumentException.class, dto::validatePagination);
}

@Test
public void testConversionToLegacyDTO() {
    // 测试转换为旧的DTO格式
    UnifiedUserGroupQueryDTO unified = UnifiedUserGroupQueryDTO.builder()
            .platform("集约平台")
            .source("dx")
            .strategyId("STRATEGY_001")
            .page(1)
            .pageSize(10)
            .build();
    
    UserGroupQueryRequestDTO legacy = unified.toPagedQueryDTO();
    assertEquals("STRATEGY_001", legacy.getStrategyId());
}

@Test
public void testStrategyIdHandling() {
    // 测试策略ID处理
    UnifiedUserGroupQueryDTO dto = UnifiedUserGroupQueryDTO.builder()
            .strategyId("STRATEGY_001")
            .build();

    assertEquals("STRATEGY_001", dto.getTrimmedStrategyId());
}
```

### 3. 集成测试

#### UserGroupIntegrationTest

```java
@Test
@Transactional
public void testEndToEndUserGroupQuery() {
    // 端到端测试：从查询到返回
    // 1. 准备测试数据
    // 2. 调用新的统一接口
    // 3. 验证返回结果
    // 4. 验证缓存更新
}

@Test
@Transactional
public void testBackwardCompatibility() {
    // 测试向后兼容性
    // 1. 调用废弃接口
    // 2. 验证返回结果与新接口一致
    // 3. 验证警告日志输出
}

@Test
@Transactional
public void testDatabaseFieldMigration() {
    // 测试数据库字段迁移
    // 1. 验证 strategy_id 字段存在
    // 2. 验证数据完整性
    // 3. 验证查询功能正常
}
```

## 🔧 测试工具类更新

### TestDataBuilder

```java
public class TestDataBuilder {
    
    public static GroupInfoDTO createGroupInfoWithStrategyId(String groupId, String strategyId) {
        return GroupInfoDTO.withStrategyId(groupId, strategyId, "测试分组", "测试描述");
    }
    
    public static UnifiedUserGroupQueryDTO createUnifiedQuery() {
        return UnifiedUserGroupQueryDTO.builder()
                .platform("集约平台")
                .source("dx")
                .page(1)
                .pageSize(10)
                .build();
    }
    
    public static UnifiedGroupQueryDTO createGroupQuery(String source) {
        return UnifiedGroupQueryDTO.forSource(source);
    }
}
```

### MockDataProvider

```java
@Component
public class MockDataProvider {
    
    public List<GroupInfoDTO> createMockGroups() {
        return Arrays.asList(
            GroupInfoDTO.withStrategyId("GROUP_001", "STRATEGY_001", "VIP用户", "高价值用户群"),
            GroupInfoDTO.withStrategyId("GROUP_002", "STRATEGY_002", "普通用户", "普通用户群")
        );
    }
    
    public UnifiedUserGroupQueryDTO createMockQuery() {
        return UnifiedUserGroupQueryDTO.builder()
                .platform("集约平台")
                .source("dx")
                .strategyId("STRATEGY_001")
                .page(1)
                .pageSize(10)
                .build();
    }
}
```

## 📊 测试覆盖率要求

### 新接口测试覆盖率
- **统一接口**：100% 覆盖率
- **参数验证**：100% 覆盖率
- **错误处理**：100% 覆盖率
- **向后兼容**：100% 覆盖率

### 测试场景覆盖

#### 正常场景
- ✅ 有效参数的各种组合
- ✅ 不同数据格式的返回
- ✅ 分页和排序功能
- ✅ 缓存命中和未命中

#### 异常场景
- ✅ 无效参数验证
- ✅ 数据库连接异常
- ✅ 缓存服务异常
- ✅ 超时和熔断

#### 边界场景
- ✅ 空数据集
- ✅ 大数据量查询
- ✅ 极限参数值
- ✅ 并发访问

## 🚀 性能测试

### 压力测试场景

```java
@Test
public void testUnifiedInterfacePerformance() {
    // 测试新统一接口的性能
    // 对比原有接口的响应时间
    // 验证性能没有退化
}

@Test
public void testConcurrentAccess() {
    // 测试并发访问
    // 验证线程安全性
    // 检查资源竞争
}
```

### 基准测试

```java
@Benchmark
public void benchmarkOldInterface() {
    // 旧接口性能基准
}

@Benchmark
public void benchmarkNewInterface() {
    // 新接口性能基准
}
```

## 📝 测试执行计划

### 阶段一：单元测试（1-2天）
1. 更新现有测试用例
2. 添加新接口测试用例
3. 添加DTO测试用例
4. 验证测试覆盖率

### 阶段二：集成测试（2-3天）
1. 端到端测试
2. 向后兼容性测试
3. 数据迁移测试
4. 缓存一致性测试

### 阶段三：性能测试（1-2天）
1. 压力测试
2. 并发测试
3. 基准对比
4. 性能回归验证

### 阶段四：验收测试（1天）
1. 业务场景验证
2. 用户体验测试
3. 文档验证
4. 部署验证

## 🔍 测试检查清单

### 功能测试
- [ ] 所有新接口功能正常
- [ ] 所有废弃接口向后兼容
- [ ] 参数验证正确
- [ ] 错误处理完善
- [ ] 返回格式正确

### 性能测试
- [ ] 响应时间符合要求
- [ ] 并发处理能力正常
- [ ] 内存使用合理
- [ ] 缓存效果良好

### 兼容性测试
- [ ] 旧客户端正常工作
- [ ] 新客户端功能完整
- [ ] 数据格式兼容
- [ ] API版本管理

### 安全测试
- [ ] 参数注入防护
- [ ] 权限验证正确
- [ ] 敏感信息保护
- [ ] 审计日志完整

## 🛠️ 测试环境配置

### 测试数据库
```yaml
spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
  jpa:
    hibernate:
      ddl-auto: create-drop
```

### 测试配置
```yaml
app:
  test:
    mock-external-services: true
    enable-cache: true
    log-level: DEBUG
```

### CI/CD 集成
```yaml
test:
  stages:
    - unit-tests
    - integration-tests
    - performance-tests
  coverage:
    minimum: 80%
  timeout: 30m
```
