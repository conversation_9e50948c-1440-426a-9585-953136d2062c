# 🔍 前后端联调监控分析方案

## 📊 监控重点与失败记录分析

### 🎯 核心监控指标

#### 1. **数据导入失败记录监控**
```bash
# 关键日志标识符
grep "流量平台-----> 导入Excel文件失败" logs/iptv-flux-service.log
grep "流量平台-----> 同步处理文件失败" logs/iptv-flux-service.log
grep "流量平台-----> 异步处理文件失败" logs/iptv-flux-service.log
```

#### 2. **批次处理失败监控**
```bash
# 批次处理失败
grep "流量平台-----> 批次.*处理失败" logs/iptv-flux-service.log
grep "流量平台-----> 处理用户ID.*的分组关系失败" logs/iptv-flux-service.log
```

#### 3. **数据库操作失败监控**
```bash
# 数据库保存失败
grep "流量平台-----> 保存用户.*的用户分组关系失败" logs/iptv-flux-service.log
grep "流量平台-----> 查找用户.*的用户分组关系失败" logs/iptv-flux-service.log
```

### 🚨 常见失败场景分析

#### **场景1: 文件格式/大小问题**
**监控日志**:
```
流量平台-----> 文件大小超过限制: 200MB，实际大小: XXXmb
流量平台-----> 上传的文件为空
不支持的文件格式，请上传.xlsx或.xls文件
```

**解决方案**:
- 检查文件大小是否超过200MB限制
- 验证文件格式是否为.xlsx或.xls
- 确认文件内容不为空

#### **场景2: 数据验证失败**
**监控日志**:
```
流量平台-----> 无效的用户ID格式: XXX
流量平台-----> 处理用户ID XXX 的分组关系失败
```

**解决方案**:
- 检查Excel中用户ID格式是否正确
- 验证用户ID是否包含特殊字符
- 确认数据行格式符合预期

#### **场景3: 数据库连接/事务问题**
**监控日志**:
```
流量平台-----> 保存用户ID: XXX, 来源: XXX 的用户分组关系失败
流量平台-----> 无法获取导入锁，任务ID: XXX
```

**解决方案**:
- 检查数据库连接状态
- 验证事务是否正常提交
- 确认分布式锁是否正常工作

#### **场景4: 内存/性能问题**
**监控日志**:
```
流量平台-----> 序列化进度失败，任务ID: XXX
流量平台-----> 保存进度到Redis失败，任务ID: XXX
```

**解决方案**:
- 检查JVM内存使用情况
- 验证Redis连接状态
- 调整批次大小参数

### 📈 实时监控命令

#### **1. 实时日志监控**
```bash
# 监控所有错误日志
tail -f logs/iptv-flux-service.log | grep -E "(ERROR|失败|异常)"

# 监控数据导入相关日志
tail -f logs/iptv-flux-service.log | grep "流量平台-----> .*导入"

# 监控进度更新
tail -f logs/iptv-flux-service.log | grep "进度已更新"
```

#### **2. 错误统计分析**
```bash
# 统计今日错误数量
grep "$(date +%Y-%m-%d)" logs/iptv-flux-service.log | grep -c "ERROR"

# 统计失败类型分布
grep "流量平台-----> .*失败" logs/iptv-flux-service.log | awk '{print $NF}' | sort | uniq -c
```

### 🔧 调试工具与接口

#### **1. 进度查询接口**
```bash
# 查询任务进度
curl -X GET "http://localhost:7003/api/data-import/progress/{taskId}"
```

#### **2. 系统状态检查**
```bash
# 健康检查
curl -X GET "http://localhost:7003/api/data-import/health"

# 性能统计
curl -X GET "http://localhost:7003/api/data-import/stats"
```

#### **3. 支持格式验证**
```bash
# 获取支持的文件格式
curl -X GET "http://localhost:7003/api/data-import/formats"
```

### 📋 联调检查清单

#### **前端检查项**
- [ ] 文件上传组件是否正确设置Content-Type
- [ ] 文件大小限制是否与后端一致(200MB)
- [ ] 进度查询是否按正确频率调用
- [ ] 错误信息是否正确显示给用户
- [ ] 异步任务状态是否正确处理

#### **后端检查项**
- [ ] 文件接收是否正常
- [ ] 数据库连接是否稳定
- [ ] Redis缓存是否正常工作
- [ ] 分布式锁是否正常获取/释放
- [ ] 批次处理大小是否合理

#### **数据检查项**
- [ ] Excel文件格式是否标准
- [ ] 用户ID格式是否正确
- [ ] 分组信息是否完整
- [ ] 数据量是否在合理范围内

### 🎯 关键性能指标

#### **处理性能**
- 每秒处理记录数: ~1000条/秒
- 批次大小: 1000条(可调整)
- 内存使用: 监控JVM堆内存
- 数据库连接: 监控连接池状态

#### **错误率阈值**
- 总体失败率: < 5%
- 单批次失败率: < 1%
- 数据库操作失败率: < 0.1%

### 🚀 优化建议

#### **性能优化**
1. 根据文件大小动态调整批次大小
2. 使用异步模式处理大文件
3. 合理设置数据库连接池参数
4. 优化Redis缓存策略

#### **错误处理优化**
1. 增加详细的错误分类
2. 提供具体的修复建议
3. 实现失败记录的重试机制
4. 增加数据验证的前置检查

### 📞 联调沟通要点

#### **与前端对接**
1. 确认文件上传参数格式
2. 统一错误码和错误信息格式
3. 确定进度查询的轮询策略
4. 约定异步任务的状态流转

#### **问题排查流程**
1. 收集完整的错误日志
2. 确认请求参数和文件内容
3. 检查系统资源使用情况
4. 验证数据库和缓存状态
5. 分析具体的失败原因
6. 提供针对性的解决方案

---

## 🔍 实时监控脚本

创建监控脚本 `monitor_import.sh`:
```bash
#!/bin/bash
echo "=== IPTV数据导入监控 ==="
echo "监控时间: $(date)"
echo "========================"

# 错误统计
echo "📊 错误统计:"
grep "$(date +%Y-%m-%d)" logs/iptv-flux-service.log | grep -c "ERROR"

# 最新错误
echo "🚨 最新错误:"
tail -20 logs/iptv-flux-service.log | grep "ERROR"

# 进度更新
echo "📈 最新进度:"
tail -10 logs/iptv-flux-service.log | grep "进度已更新"
```
