# businessId 到 strategyId 完整迁移说明

## 📋 迁移概述

根据你的要求，我已经将代码库中所有使用 `businessId` 的地方完全替换为 `strategyId`，不再保持向后兼容性。这是一次彻底的字段重命名迁移。

## 🔄 已完成的代码变更

### 1. 实体类更新

#### GroupInfo.java
```java
// 原字段
private String businessId;

// 新字段
private String strategyId;
```

### 2. DTO 类更新

#### GroupInfoDTO.java
- ✅ 移除 `businessId` 字段
- ✅ 保留 `strategyId` 字段
- ✅ 简化业务方法，移除兼容性逻辑
- ✅ 更新工厂方法

#### GroupQueryRequestDTO.java
- ✅ 移除 `businessId` 字段
- ✅ 保留 `strategyId` 字段
- ✅ 更新 `getTrimmedStrategyId()` 方法
- ✅ 简化查询逻辑

#### UnifiedGroupQueryDTO.java
- ✅ 移除 `businessId` 字段
- ✅ 更新所有相关方法
- ✅ 简化转换逻辑

#### UnifiedUserGroupQueryDTO.java
- ✅ 移除 `businessId` 字段相关逻辑
- ✅ 更新转换方法
- ✅ 简化验证逻辑

#### DataImportRequestDTO.java
- ✅ 将 `businessId` 字段替换为 `strategyId`

### 3. Repository 层更新

#### GroupInfoRepository.java
- ✅ 数据库字段常量：`BUSINESS_ID` → `STRATEGY_ID`
- ✅ 查询方法：使用 `strategy_id` 字段
- ✅ 保存方法：使用 `strategy_id` 字段
- ✅ 条件构建：使用 `STRATEGY_ID` 字段
- ✅ 记录转换：映射到 `strategyId` 字段

### 4. Service 层更新

#### GroupInfoService.java
- ✅ 更新实体转换逻辑
- ✅ 更新分组创建逻辑
- ✅ 更新分组更新逻辑
- ✅ 移除向后兼容代码

### 5. Controller 层更新

#### UserGroupController.java
- ✅ 更新方法调用中的字段引用
- ✅ 修正转换逻辑

## 🗄️ 数据库迁移

### 迁移脚本
已创建 `scripts/migrate_businessid_to_strategyid.sql` 脚本，包含：

1. **添加新字段**
```sql
ALTER TABLE group_info ADD COLUMN strategy_id VARCHAR(255) COMMENT '策略ID';
```

2. **数据迁移**
```sql
UPDATE group_info SET strategy_id = business_id WHERE business_id IS NOT NULL;
```

3. **添加索引**
```sql
CREATE INDEX idx_group_info_strategy_id ON group_info(strategy_id);
```

4. **验证迁移**
```sql
SELECT COUNT(*) as total_records, COUNT(strategy_id) as strategy_id_count 
FROM group_info;
```

### 迁移步骤
1. **备份数据库**（必须！）
2. 执行迁移脚本
3. 验证数据完整性
4. 部署新代码
5. 验证功能正常
6. 可选：删除旧的 `business_id` 字段

## 📝 API 变更影响

### 请求参数变更
```json
// 原请求格式
{
  "groupName": "VIP用户组",
  "businessId": "biz001",
  "source": "dx"
}

// 新请求格式
{
  "groupName": "VIP用户组", 
  "strategyId": "STRATEGY_001",
  "source": "dx"
}
```

### 响应数据变更
```json
// 原响应格式
{
  "groupId": "GROUP_001",
  "businessId": "biz001",
  "groupName": "VIP用户组"
}

// 新响应格式
{
  "groupId": "GROUP_001",
  "strategyId": "STRATEGY_001", 
  "groupName": "VIP用户组"
}
```

## 🔧 客户端迁移指南

### 1. 前端代码更新
```javascript
// 原代码
const request = {
  groupName: "VIP用户组",
  businessId: "biz001",
  source: "dx"
};

// 新代码
const request = {
  groupName: "VIP用户组",
  strategyId: "STRATEGY_001",
  source: "dx"
};
```

### 2. 后端服务调用更新
```java
// 原代码
GroupQueryRequestDTO request = GroupQueryRequestDTO.builder()
    .groupName("VIP用户组")
    .businessId("biz001")
    .source("dx")
    .build();

// 新代码
GroupQueryRequestDTO request = GroupQueryRequestDTO.builder()
    .groupName("VIP用户组")
    .strategyId("STRATEGY_001")
    .source("dx")
    .build();
```

### 3. 数据处理逻辑更新
```java
// 原代码
String businessId = groupInfo.getBusinessId();

// 新代码
String strategyId = groupInfo.getStrategyId();
```

## ⚠️ 重要注意事项

### 1. 破坏性变更
- 这是一次**破坏性变更**
- 所有使用 `businessId` 的客户端都需要更新
- 必须同时更新数据库和应用代码

### 2. 部署顺序
1. **数据库迁移**（先执行）
2. **应用代码部署**（后执行）
3. **客户端更新**（最后执行）

### 3. 回滚计划
- 保留数据库备份
- 保留旧版本代码
- 制定回滚步骤

## 📊 验证清单

### 数据库验证
- [ ] `strategy_id` 字段已创建
- [ ] 数据已完整迁移
- [ ] 索引已创建
- [ ] 查询性能正常

### 应用验证
- [ ] 所有接口正常响应
- [ ] 查询功能正确
- [ ] 保存功能正确
- [ ] 日志无错误

### 集成验证
- [ ] 前端调用正常
- [ ] 第三方服务调用正常
- [ ] 数据导入导出正常
- [ ] 缓存功能正常

## 🚀 部署建议

### 1. 测试环境验证
- 完整执行迁移流程
- 验证所有功能正常
- 性能测试通过

### 2. 生产环境部署
- 选择低峰时段
- 准备回滚方案
- 监控系统状态
- 及时处理问题

### 3. 监控指标
- API 响应时间
- 错误率统计
- 数据库性能
- 客户端调用情况

## 📞 支持联系

如果在迁移过程中遇到问题，请：
1. 检查日志文件
2. 验证数据库状态
3. 确认配置正确
4. 联系技术支持

---

**重要提醒：这是一次不可逆的迁移，请务必在生产环境执行前在测试环境充分验证！**
