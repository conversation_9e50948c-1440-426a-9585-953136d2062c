# UserGroupController API 重构说明

## 📋 重构概述

本次重构主要解决了 UserGroupController 中存在的接口重复问题，通过合并功能相似的接口，简化了 API 结构，同时保持了完全的向后兼容性。

## 🔄 接口变更对照表

### 1. 用户分组查询接口

| 原接口 | 新接口 | 状态 | 说明 |
|--------|--------|------|------|
| `GET /{source}/{userId}` | `GET /{source}/{userId}?includeDetail=false` | ✅ 统一 | 返回分组ID集合 |
| `GET /{source}/{userId}/detail` | `GET /{source}/{userId}?includeDetail=true` | ⚠️ 废弃 | 返回详细信息映射 |

**新接口特性：**
- 通过 `includeDetail` 参数控制返回格式
- 默认 `includeDetail=false`，保持原有行为
- 支持向后兼容的 `/detail` 路径

### 2. 分组信息查询接口

| 原接口 | 新接口 | 状态 | 说明 |
|--------|--------|------|------|
| `GET /groups` | `POST /groups/query` | ⚠️ 废弃 | 查询所有分组 |
| `GET /groups/{source}` | `POST /groups/query` | ⚠️ 废弃 | 按来源查询分组 |
| `POST /groups/query` | `POST /groups/query` | ✅ 统一 | 条件查询分组 |

**新接口特性：**
- 统一使用 `POST /groups/query` 接口
- 支持 `UnifiedGroupQueryDTO` 参数
- 空参数时返回所有分组
- 只传 `source` 时按来源查询

### 3. 分页查询接口

| 原接口 | 新接口 | 状态 | 说明 |
|--------|--------|------|------|
| `GET /list` | `POST /query/page` | ⚠️ 废弃 | 简单分页查询 |
| `GET /platform/page` | `POST /query/page` | ⚠️ 废弃 | 复杂分页查询 |

**新接口特性：**
- 统一使用 `POST /query/page` 接口
- 支持 `UnifiedUserGroupQueryDTO` 参数
- 自动识别简单查询和复杂查询
- 兼容两种返回格式

### 4. 全量查询和导出接口

| 原接口 | 新接口 | 状态 | 说明 |
|--------|--------|------|------|
| `GET /platform/all` | `POST /query/data?format=json` | ⚠️ 废弃 | 全量查询 |
| `GET /platform/export` | `POST /query/data?format=export` | ⚠️ 废弃 | 流式导出 |

**新接口特性：**
- 统一使用 `POST /query/data` 接口
- 通过 `format` 参数控制输出格式
- `format=json` 返回 JSON 数据
- `format=export` 流式导出文件

### 5. 黑白名单管理接口

| 原接口 | 新接口 | 状态 | 说明 |
|--------|--------|------|------|
| `POST /blacklist` | `POST /list/blacklist` | ⚠️ 废弃 | 查询黑名单 |
| `POST /blacklist/add` | `POST /list/blacklist/add` | ⚠️ 废弃 | 添加黑名单 |
| `DELETE /blacklist/{id}` | `DELETE /list/blacklist/{id}` | ⚠️ 废弃 | 删除黑名单 |
| `POST /whitelist` | `POST /list/whitelist` | ⚠️ 废弃 | 查询白名单 |
| `POST /whitelist/add` | `POST /list/whitelist/add` | ⚠️ 废弃 | 添加白名单 |
| `DELETE /whitelist/{id}` | `DELETE /list/whitelist/{id}` | ⚠️ 废弃 | 删除白名单 |

**新接口特性：**
- 统一使用 `/list/{listType}` 路径模式
- `listType` 支持 `blacklist` 和 `whitelist`
- 泛化的列表管理逻辑

## 🆕 新增 DTO 类

### 1. UnifiedUserGroupQueryDTO
```java
// 统一的用户分组查询参数
{
  "platform": "集约平台",
  "source": "dx",
  "userId": "user123",
  "groupId": "GROUP_001",
  "strategyId": "STRATEGY_001",
  "businessId": "biz001", // 已废弃
  "includeDetail": false,
  "format": "json",
  "page": 1,
  "pageSize": 10
}
```

### 2. UnifiedGroupQueryDTO
```java
// 统一的分组查询参数
{
  "groupName": "VIP用户组",
  "strategyId": "STRATEGY_001",
  "businessId": "biz001", // 已废弃
  "source": "dx",
  "groupId": "GROUP_001"
}
```

### 3. UnifiedListManagementDTO
```java
// 统一的列表管理参数
{
  "listType": "blacklist", // 或 "whitelist"
  "userId": "user123",
  "source": "dx",
  "remark": "恶意用户",
  "page": 1,
  "pageSize": 10
}
```

## 🔄 字段迁移说明

### businessId → strategyId

| 类名 | 原字段 | 新字段 | 迁移状态 |
|------|--------|--------|----------|
| `GroupInfoDTO` | `businessId` | `strategyId` | ✅ 完全替换 |
| `GroupQueryRequestDTO` | `businessId` | `strategyId` | ✅ 完全替换 |
| `UnifiedGroupQueryDTO` | `businessId` | `strategyId` | ✅ 完全替换 |
| `UnifiedUserGroupQueryDTO` | `businessId` | `strategyId` | ✅ 完全替换 |
| `GroupInfo` (实体类) | `businessId` | `strategyId` | ✅ 完全替换 |
| `DataImportRequestDTO` | `businessId` | `strategyId` | ✅ 完全替换 |

**迁移策略：**
- 所有 `businessId` 字段已完全替换为 `strategyId`
- 数据库字段从 `business_id` 迁移到 `strategy_id`
- 提供数据库迁移脚本确保数据完整性
- 所有相关方法和查询已更新

## 📝 使用示例

### 1. 用户分组查询
```bash
# 获取分组ID集合（新方式）
curl "http://localhost:7003/api/usergroup/dx/user123?includeDetail=false"

# 获取详细信息（新方式）
curl "http://localhost:7003/api/usergroup/dx/user123?includeDetail=true"

# 向后兼容（旧方式，仍可用但会有废弃警告）
curl "http://localhost:7003/api/usergroup/dx/user123/detail"
```

### 2. 分组信息查询
```bash
# 查询所有分组（新方式）
curl -X POST "http://localhost:7003/api/usergroup/groups/query" \
  -H "Content-Type: application/json" \
  -d '{}'

# 按来源查询（新方式）
curl -X POST "http://localhost:7003/api/usergroup/groups/query" \
  -H "Content-Type: application/json" \
  -d '{"source": "dx"}'

# 条件查询（新方式）
curl -X POST "http://localhost:7003/api/usergroup/groups/query" \
  -H "Content-Type: application/json" \
  -d '{"groupName": "VIP", "strategyId": "STRATEGY_001"}'
```

### 3. 分页查询
```bash
# 统一分页查询（新方式）
curl -X POST "http://localhost:7003/api/usergroup/query/page" \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "集约平台",
    "source": "dx",
    "page": 1,
    "pageSize": 10
  }'
```

### 4. 全量查询和导出
```bash
# 全量查询（新方式）
curl -X POST "http://localhost:7003/api/usergroup/query/data" \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "集约平台",
    "format": "json",
    "maxLimit": 10000
  }'

# 导出文件（新方式）
curl -X POST "http://localhost:7003/api/usergroup/query/data" \
  -H "Content-Type: application/json" \
  -d '{
    "platform": "集约平台",
    "format": "export",
    "maxLimit": 100000
  }' --output usergroups.json
```

### 5. 黑白名单管理
```bash
# 查询黑名单（新方式）
curl -X POST "http://localhost:7003/api/usergroup/list/blacklist" \
  -H "Content-Type: application/json" \
  -d '{"page": 1, "pageSize": 10}'

# 添加到黑名单（新方式）
curl -X POST "http://localhost:7003/api/usergroup/list/blacklist/add" \
  -H "Content-Type: application/json" \
  -d '{
    "listType": "blacklist",
    "userId": "user123",
    "source": "dx",
    "remark": "恶意用户"
  }'

# 从黑名单删除（新方式）
curl -X DELETE "http://localhost:7003/api/usergroup/list/blacklist/1"
```

## ⚠️ 迁移建议

### 立即行动
1. **更新客户端代码**：使用新的统一接口
2. **更新参数结构**：使用新的 DTO 类
3. **字段迁移**：将 `businessId` 替换为 `strategyId`

### 渐进迁移
1. **第一阶段**：新功能使用新接口
2. **第二阶段**：逐步迁移现有调用
3. **第三阶段**：完全移除废弃接口（预计6个月后）

### 监控指标
- 废弃接口的调用频率
- 新接口的采用率
- 错误率和性能指标

## 🔧 开发者注意事项

1. **日志监控**：废弃接口会输出警告日志
2. **性能优化**：新接口支持更好的缓存策略
3. **错误处理**：统一的错误响应格式
4. **文档更新**：Swagger 文档已更新
5. **测试覆盖**：需要更新相关测试用例

## 📊 重构收益

- **接口数量减少**：从 15+ 个接口减少到 6 个核心接口
- **参数统一**：使用统一的 DTO 结构
- **维护简化**：减少重复代码和逻辑
- **扩展性增强**：更容易添加新功能
- **向后兼容**：现有客户端无需立即修改
