# 黑白名单缓存清理机制

## 🎯 概述

为了保证黑白名单缓存与数据库的一致性，我们实现了完整的缓存清理机制。当黑白名单数据发生变更时，相关缓存会被自动清理。

## 🔧 实现方案

### 1. 避免循环依赖

**问题**：最初设计中，BlacklistService 和 WhitelistService 需要调用 UserGroupService 来清理缓存，这会造成循环依赖。

**解决方案**：直接在黑白名单服务中注入 StringRedisTemplate，自行处理缓存清理。

```java
// BlacklistService 和 WhitelistService 中
@Autowired
private StringRedisTemplate redisTemplate;
```

### 2. 缓存清理实现

#### 2.1 添加用户时清理缓存

```java
@Transactional(rollbackFor = Exception.class)
public void addUser(BlacklistUserDTO dto) {
    // 添加到数据库
    blacklistRepository.addUser(dto);
    
    // 清理相关缓存
    clearUserCache(dto.getUserId(), dto.getSource());
    
    log.info("流量平台-----> 成功添加黑名单用户并清理缓存: {}, 来源: {}", 
            dto.getUserId(), dto.getSource());
}
```

#### 2.2 删除用户时清理缓存

```java
@Transactional(rollbackFor = Exception.class)
public void deleteUser(String id) {
    // 先查询用户信息，用于清理缓存
    BlacklistUserDTO userInfo = blacklistRepository.findById(id);
    if (userInfo == null) {
        throw new IllegalArgumentException("黑名单记录不存在: " + id);
    }

    // 删除用户
    boolean success = blacklistRepository.deleteUser(id);
    if (!success) {
        throw new IllegalArgumentException("删除黑名单记录失败: " + id);
    }

    // 清理相关缓存
    clearUserCache(userInfo.getUserId(), userInfo.getSource());
}
```

**注意**：为了支持删除时的缓存清理，我们在 BlacklistRepository 和 WhitelistRepository 中添加了 `findById` 方法：

```java
/**
 * 根据ID查询黑名单用户
 */
@Timed(value = "repository.blacklist.findById", percentiles = {0.5, 0.95, 0.99})
public BlacklistUserDTO findById(String id) {
    try {
        Record record = dsl.select()
                .from(table(BLACKLIST_TABLE))
                .where(ID.eq(Long.valueOf(id)))
                .fetchOne();

        if (record != null) {
            return convertToDTO(record);
        }
        return null;
    } catch (Exception e) {
        log.error("流量平台-----> 根据ID查询黑名单用户失败: {}", id, e);
        throw e;
    }
}
```

#### 2.3 缓存清理核心方法

```java
/**
 * 清理用户相关缓存
 */
private void clearUserCache(String userId, String source) {
    try {
        String compositeKey = CacheKeyBuilder.buildUserGroupCompositeKey(source, userId);
        String blackWhiteListCacheKey = "bwl:" + compositeKey;
        
        // 清理黑白名单缓存
        redisTemplate.delete(blackWhiteListCacheKey);
        
        log.debug("流量平台-----> 已清理用户黑白名单缓存: {}, 来源: {}", userId, source);
    } catch (Exception e) {
        log.warn("流量平台-----> 清理用户黑白名单缓存失败: {}, 来源: {}", userId, source, e);
    }
}
```

## 🔄 缓存清理时机

### 1. 自动清理时机

| 操作 | 清理时机 | 清理内容 |
|------|----------|----------|
| 添加黑名单用户 | 数据库操作成功后 | 该用户的黑白名单缓存 |
| 删除黑名单用户 | 数据库操作成功后 | 该用户的黑白名单缓存 |
| 添加白名单用户 | 数据库操作成功后 | 该用户的黑白名单缓存 |
| 删除白名单用户 | 数据库操作成功后 | 该用户的黑白名单缓存 |

### 2. 缓存键格式

```bash
# 黑白名单缓存键
bwl:{source}:{userId}

# 示例
bwl:dx:user123
bwl:lt:user456
bwl:yd:user789
```

## 🛡️ 异常处理

### 1. 缓存清理失败处理

```java
private void clearUserCache(String userId, String source) {
    try {
        // 缓存清理逻辑...
    } catch (Exception e) {
        // 记录警告日志，但不影响主流程
        log.warn("流量平台-----> 清理用户黑白名单缓存失败: {}, 来源: {}", userId, source, e);
        // 不抛出异常，确保数据库操作不受影响
    }
}
```

### 2. 设计原则

- **数据库优先**：缓存清理失败不影响数据库操作
- **最终一致性**：通过TTL保证最终一致性
- **优雅降级**：缓存问题不影响核心功能

## 📊 监控和调试

### 1. 日志记录

```java
// 成功清理
log.debug("流量平台-----> 已清理用户黑白名单缓存: {}, 来源: {}", userId, source);

// 清理失败
log.warn("流量平台-----> 清理用户黑白名单缓存失败: {}, 来源: {}", userId, source, e);
```

### 2. 手动清理命令

```bash
# 清理特定用户的黑白名单缓存
redis-cli del "bwl:dx:user123"

# 批量清理某个来源的缓存
redis-cli --scan --pattern "bwl:dx:*" | xargs redis-cli del

# 查看缓存内容
redis-cli get "bwl:dx:user123"
```

## 🔍 验证方法

### 1. 功能验证

```bash
# 1. 添加黑名单用户
curl -X POST /api/usergroup/blacklist/add -d '{"userId":"test001","source":"dx"}'

# 2. 查询用户分组（应该返回空）
curl /api/usergroup/dx/test001

# 3. 检查缓存是否存在
redis-cli get "bwl:dx:test001"  # 应该返回 "BLACKLIST"

# 4. 删除黑名单用户
curl -X DELETE /api/usergroup/blacklist/{id}

# 5. 检查缓存是否被清理
redis-cli get "bwl:dx:test001"  # 应该返回 (nil)
```

### 2. 性能验证

```bash
# 监控缓存清理性能
redis-cli monitor | grep "bwl:"

# 查看缓存命中率
curl /api/monitor/metrics | grep blackwhitelist
```

## ✅ 总结

通过这个缓存清理机制，我们实现了：

1. **数据一致性**：黑白名单变更时自动清理相关缓存
2. **无循环依赖**：直接使用 Redis 客户端，避免服务间循环依赖
3. **异常安全**：缓存清理失败不影响数据库操作
4. **完整覆盖**：添加和删除操作都包含缓存清理
5. **易于调试**：详细的日志记录和手动清理命令

这确保了黑白名单功能的高可靠性和数据一致性！
